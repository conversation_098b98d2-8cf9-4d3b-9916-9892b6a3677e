import { Badge } from '@/components/ui/badge';
import type { ClientTag } from '../types';

interface TagDisplayProps {
  tags: ClientTag[];
  maxVisible?: number;
  className?: string;
}

const TagDisplay = ({
  tags,
  maxVisible = 3,
  className = ""
}: TagDisplayProps) => {
  if (!tags || tags.length === 0) {
    return null;
  }

  const visibleTags = tags.slice(0, maxVisible);
  const hiddenCount = tags.length - maxVisible;

  return (
    <div className={`flex flex-wrap gap-1 ${className}`}>
      {visibleTags.map(tag => (
        <Badge
          key={tag.id}
          variant="outline"
          className="text-xs"
        >
          {tag.name}
        </Badge>
      ))}

      {hiddenCount > 0 && (
        <Badge
          variant="outline"
          className="text-xs"
        >
          +{hiddenCount}
        </Badge>
      )}
    </div>
  );
};

export default TagDisplay;
