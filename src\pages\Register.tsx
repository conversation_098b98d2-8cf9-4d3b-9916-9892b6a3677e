
import { Link, Navigate } from 'react-router-dom';
import AuthForm from '@/components/auth/AuthForm';
import { Card, CardContent } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { useSearchParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import InvitationRegistrationForm from '@/components/auth/InvitationRegistrationForm';

const Register = () => {
  const { user, profile } = useAuth();
  const [searchParams] = useSearchParams();
  const [authError, setAuthError] = useState<string | null>(null);
  const invitationToken = searchParams.get('invitation');

  useEffect(() => {
    // Check URL parameters for auth related errors
    const error = searchParams.get('error');
    if (error) {
      setAuthError(decodeURIComponent(error));
    }
  }, [searchParams]);

  // If user is already authenticated, redirect to the appropriate dashboard
  if (user) {
    if (profile?.user_type === 'artist') {
      // Store user type in local storage for fallback
      localStorage.setItem('user_type', 'artist');
      return <Navigate to="/artist/dashboard" replace />;
    } else if (profile?.user_type === 'venue') {
      // Store user type in local storage for fallback
      localStorage.setItem('user_type', 'venue');
      return <Navigate to="/venue/dashboard" replace />;
    } else {
      // Default fallback if profile is not loaded yet but user is authenticated
      const lastKnownUserType = localStorage.getItem('user_type');
      if (lastKnownUserType === 'artist') {
        return <Navigate to="/artist/dashboard" replace />;
      }
      return <Navigate to="/venue/dashboard" replace />;
    }
  }

  // If there's an invitation token, show the invitation registration form
  if (invitationToken) {
    return <InvitationRegistrationForm />;
  }

  // Otherwise show the regular registration form
  return (
    <div className="min-h-screen flex flex-col justify-center items-center bg-gray-50 py-[50px]">
      <div className="w-full max-w-md px-4 sm:px-0">
        <Card className="shadow-md">
          <CardContent className="p-8">
            <div className="text-center mb-6">
              <h2 className="mt-4 text-2xl font-bold text-gray-900">
                Create an account
              </h2>
              <p className="mt-2 text-sm text-gray-600">
                Already have an account?{' '}
                <Link to="/login" className="font-medium text-stagecloud-black hover:underline">
                  Log in
                </Link>
              </p>
            </div>

            {authError && (
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{authError}</AlertDescription>
              </Alert>
            )}

            <AuthForm type="register" />
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Register;
