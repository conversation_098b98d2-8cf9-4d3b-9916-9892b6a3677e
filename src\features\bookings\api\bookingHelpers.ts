import { supabase } from '@/core/api/supabase';
import { getArtistDetailsForBooking } from '@/features/entities/api/artistHelpers';
import { getVenueDetailsForBooking } from '@/features/entities/api/entityProfileHelpers';
import { getUserProfileForBooking } from '@/features/entities/api/profileHelpers';

/**
 * Get booking details
 */
export const getBookingDetails = async (bookingId: string) => {
  try {
    // Get the booking data
    const { data: bookingData, error: bookingError } = await supabase
      .from('bookings')
      .select('*')
      .eq('id', bookingId)
      .single();

    if (bookingError) {
      console.error('Error fetching booking details:', bookingError);
      throw new Error(`Failed to fetch booking with ID ${bookingId}: ${bookingError.message}`);
    }

    if (!bookingData) {
      throw new Error(`Booking with ID ${bookingId} not found`);
    }

    // Fetch all related data in parallel to improve performance
    const [artistDetailsPromise, venueDetailsPromise, artistProfilePromise, venueProfilePromise, creatorProfilePromise] = [
      getArtistDetailsForBooking(bookingData.artist_id),
      getVenueDetailsForBooking(bookingData.venue_id),
      getUserProfileForBooking(bookingData.artist_id),
      getUserProfileForBooking(bookingData.venue_id),
      getUserProfileForBooking(bookingData.created_by)
    ];

    // Wait for all promises to resolve
    const [artistDetails, venueDetails, artistProfileData, venueProfileData, creatorProfileData] =
      await Promise.all([
        artistDetailsPromise.catch(err => {
          console.error(`Error fetching artist details for booking ${bookingId}:`, err);
          // Return a placeholder instead of null
          return { artist_name: 'Unknown Artist', profile_image_url: null };
        }),
        venueDetailsPromise.catch(err => {
          console.error(`Error fetching venue details for booking ${bookingId}:`, err);
          // Return a placeholder instead of null
          return { venue_name: 'Unknown Venue', address: null };
        }),
        artistProfilePromise.catch(err => {
          console.error(`Error fetching artist profile for booking ${bookingId}:`, err);
          // Return a placeholder instead of null
          return { id: bookingData.artist_id, name: 'Unknown Artist', email: null };
        }),
        venueProfilePromise.catch(err => {
          console.error(`Error fetching venue profile for booking ${bookingId}:`, err);
          // Return a placeholder instead of null
          return { id: bookingData.venue_id, name: 'Unknown Venue', email: null };
        }),
        creatorProfilePromise.catch(err => {
          console.error(`Error fetching creator profile for booking ${bookingId}:`, err);
          // Return a placeholder instead of null
          return { id: bookingData.created_by, name: 'Unknown User', email: null };
        })
      ]);

    // Log success message
    console.log(`Successfully fetched all details for booking ${bookingId}`);

    // Combine all data
    return {
      booking: bookingData,
      artist: {
        details: artistDetails || { artist_name: 'Unknown Artist', profile_image_url: null },
        profile: artistProfileData || { id: bookingData.artist_id, name: 'Unknown Artist', email: null }
      },
      venue: {
        details: venueDetails || { venue_name: 'Unknown Venue', address: null },
        profile: venueProfileData || { id: bookingData.venue_id, name: 'Unknown Venue', email: null }
      },
      creator: creatorProfileData || { id: bookingData.created_by, name: 'Unknown User', email: null }
    };
  } catch (error) {
    console.error('Exception in getBookingDetails:', error);
    throw error; // Propagate the error to allow proper handling in the UI
  }
};

/**
 * Get booking documents
 */
export const getBookingDocuments = async (bookingId: string) => {
  try {
    const { data, error } = await supabase
      .from('documents')
      .select(`
        id,
        title,
        document_type,
        file_url,
        content,
        status,
        signed_by_artist,
        signed_by_venue,
        created_at,
        updated_at,
        description,
        booking_id,
        created_by
      `)
      .eq('booking_id', bookingId);

    if (error) {
      console.error('Error fetching documents:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception in getBookingDocuments:', error);
    return null;
  }
};
