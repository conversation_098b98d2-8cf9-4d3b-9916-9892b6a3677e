import { ReactNode, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Sidebar from './Sidebar';
import { User, Bell, Settings, LogOut, Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/AuthContext';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import BreadcrumbNavigation from '../navigation/BreadcrumbNavigation';
import { useIsMobile } from '@/core';
type DashboardLayoutProps = {
  children: ReactNode;
  userType: 'venue' | 'artist' | 'agency';
};
const DashboardLayout = ({
  children,
  userType
}: DashboardLayoutProps) => {
  const {
    profile,
    signOut
  } = useAuth();
  const navigate = useNavigate();
  const isMobile = useIsMobile();
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);
  // const [notificationCount] = useState(3); // Example notification count

  // Debug profile information
  ('DashboardLayout - Profile:', profile);
  ('DashboardLayout - Profile Image URL:', profile?.profile_image_url);

  // Example notifications
  const notifications = [{
    id: 1,
    title: 'Soon, notifications will be visible here.',
    time: 'Now'
  }];
  const handleNavigation = (path: string) => {
    navigate(path);
  };
  return <div className="flex h-screen w-full">
      <Sidebar
        userType={userType}
        isMobile={isMobile}
        isOpen={mobileSidebarOpen}
        onClose={() => setMobileSidebarOpen(false)}
      />
      <div className="flex-1 flex flex-col h-screen overflow-hidden">
        {/* Dashboard header */}
        <header className="bg-white border-b border-gray-200 py-2 px-4 sticky top-0 z-10 h-14 flex items-center">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center">
              {isMobile && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="mr-2 rounded-md h-10 w-10 flex items-center justify-center text-gray-500 hover:bg-gray-100 hover:text-gray-900"
                  onClick={() => setMobileSidebarOpen(!mobileSidebarOpen)}
                >
                  <Menu className="h-5 w-5" />
                </Button>
              )}
              <div className="flex-1 min-w-0 overflow-hidden">
                <BreadcrumbNavigation userType={userType} />
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {/* Notifications dropdown - styled to match sidebar */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="rounded-md h-10 w-10 flex items-center justify-center relative text-gray-500 hover:bg-gray-100 hover:text-gray-900">
                    <Bell className="h-5 w-5" />
                    {/* {notificationCount > 0 && <span className="absolute top-[2px] right-[2px] h-4 w-4 bg-red-500 rounded-full flex items-center justify-center text-[10px] font-medium text-white">
                        {notificationCount}
                      </span>} */}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-80 bg-white shadow-md border border-gray-200">
                  <DropdownMenuLabel>Notifications</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {notifications.map(notification => <DropdownMenuItem key={notification.id} className="cursor-pointer py-1">
                      <div className="flex flex-col w-full">
                        <span className="font-medium text-sm">{notification.title}</span>
                        <span className="text-xs text-gray-500">{notification.time}</span>
                      </div>
                    </DropdownMenuItem>)}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="cursor-pointer text-center">
                    <span className="w-full text-center text-xs font-medium">View all notifications</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Profile dropdown - styled to match sidebar */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="rounded-md h-10 w-10 flex items-center justify-center text-gray-500 hover:bg-gray-100 hover:text-gray-900 p-0 overflow-hidden">
                    {profile?.profile_image_url ? (
                      <img
                        src={profile.profile_image_url}
                        alt={profile.name || 'Profile'}
                        className="h-full w-full object-cover"
                      />
                    ) : (
                      <div className="h-full w-full bg-gray-200 flex items-center justify-center text-gray-700 font-medium">
                        {profile?.name?.charAt(0).toUpperCase() || 'U'}
                      </div>
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56 bg-white shadow-md border border-gray-200">
                  <div className="px-4 py-2">
                    {profile?.entity?.type && (
                      <Badge
                        variant="outline"
                        className="text-[10px] capitalize mb-1"
                      >
                        {profile.entity.type}
                      </Badge>
                    )}
                    <p className="text-sm font-medium text-gray-900">{profile?.name || 'User'}</p>
                    <p className="text-xs text-gray-500 truncate">{profile?.email || 'No email'}</p>
                  </div>

                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="cursor-pointer hover:bg-gray-100" onClick={() => handleNavigation(`/${userType}/profile`)}>
                    <User className="mr-2 h-4 w-4" />
                    <span>Profile</span>
                  </DropdownMenuItem>

                  <DropdownMenuItem className="cursor-pointer hover:bg-gray-100" onClick={() => handleNavigation(`/${userType}/settings`)}>
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Settings</span>
                  </DropdownMenuItem>
                  {/* {{userType === 'venue' && (
                    <DropdownMenuItem className="cursor-pointer hover:bg-gray-100" onClick={() => handleNavigation('/templates')}>
                      <FileText className="mr-2 h-4 w-4" />
                      <span>Contract Templates</span>
                    </DropdownMenuItem>
                  )} */}
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className="cursor-pointer hover:bg-gray-100 text-red-600 hover:text-red-700 hover:bg-red-50" onClick={signOut}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </header>

        <main className="flex-1 overflow-y-auto bg-stagecloud-gray-100 p-4">
          {children}
        </main>
      </div>
    </div>;
};
export default DashboardLayout;