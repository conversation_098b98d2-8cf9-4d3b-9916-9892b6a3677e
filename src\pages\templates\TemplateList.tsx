import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from 'sonner';
import { FileText, Search, Loader2, Pencil, Copy, Trash2, MoreVertical, RotateCcw, FileEdit, CalendarIcon, Eye, Plus } from 'lucide-react';
import { supabase } from '@/core/api/supabase';
import { getUserEntity } from '@/features/entities/api/profileHelpers';
import { useAuth } from '@/contexts/AuthContext';
import { format, isAfter, isBefore } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { DeleteConfirmationDialog } from '@/components/ui/delete-confirmation-dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { cn } from '@/core';

interface Template {
  id: string;
  title: string;
  description: string;
  created_at: string;
  updated_at: string;
  owner_entity_id: string;
  venue_id?: string;
  variables?: any[];
  document_type?: string;
}

const TemplateList = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [venueId, setVenueId] = useState<string | null>(null);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [templateToDelete, setTemplateToDelete] = useState<string | null>(null);

  // Filtering states
  const [timeFilter, setTimeFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: undefined,
    to: undefined
  });

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);



  useEffect(() => {
    if (user) {
      getCurrentVenue();
    }
  }, [user]);

  useEffect(() => {
    if (venueId) {
      fetchTemplates();
    }
  }, [venueId]);

  // Filter templates based on search query and filters
  const filteredTemplates = templates.filter(template => {
    // Time filter
    const updatedDate = new Date(template.updated_at || template.created_at);
    if (timeFilter === 'recent' && isBefore(updatedDate, new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))) return false;
    if (timeFilter === 'older' && isAfter(updatedDate, new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))) return false;

    // Type filter
    if (typeFilter !== 'all' && template.document_type !== typeFilter) return false;

    // Date range filter
    if (dateRange?.from && isBefore(updatedDate, dateRange.from)) return false;
    if (dateRange?.to && isAfter(updatedDate, dateRange.to)) return false;

    // Search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const variableMatch = template.variables ?
        template.variables.some((v: any) =>
          v.label?.toLowerCase().includes(query) ||
          v.name?.toLowerCase().includes(query)
        ) : false;

      return (
        template.title.toLowerCase().includes(query) ||
        (template.description && template.description.toLowerCase().includes(query)) ||
        variableMatch
      );
    }

    return true;
  });

  const getCurrentVenue = async () => {
    try {
      if (!user) return;

      // Get the user's entity ID directly - works for all entity types (venue, agency, artist)
      const userEntityData = await getUserEntity(user.id);

      if (!userEntityData || !userEntityData.entity_id) {
        console.error('Error fetching user entity');
        toast.error("Could not find your entity information");
        return;
      }

      // Set the entity ID as the venue ID (for backward compatibility)
      setVenueId(userEntityData.entity_id);

      ('Using entity ID for templates:', userEntityData.entity_id, 'Entity type:', userEntityData.entity_type);
    } catch (error) {
      console.error('Error getting user entity:', error);
      toast.error('Failed to get entity information');
    }
  };

  const fetchTemplates = async () => {
    if (!venueId) return;

    setLoading(true);
    try {
      ('Fetching templates with entity ID:', venueId);

      // Use getDocumentTemplates function which already handles entity ID correctly
      const { getDocumentTemplates } = await import('@/features/documents/api/documentHelpers');
      const templatesData = await getDocumentTemplates(venueId);

      if (!templatesData) {
        throw new Error('Could not fetch templates');
      }

      (`Found ${templatesData.length} templates`);

      // Transform the data to match our Template interface
      const formattedTemplates = (templatesData || []).map((template: any) => ({
        id: template.id,
        title: template.title,
        description: template.description || '',
        created_at: template.created_at,
        updated_at: template.updated_at,
        owner_entity_id: template.owner_entity_id,
        venue_id: template.venue_id,
        document_type: template.document_type || 'contract',
        variables: template.variables ?
          (typeof template.variables === 'string' ?
            JSON.parse(template.variables) :
            template.variables) :
          []
      }));

      setTemplates(formattedTemplates);
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast.error("Error loading templates. There was a problem retrieving document templates.");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTemplate = async () => {
    if (!templateToDelete) return;

    try {
      // First check if there are any documents using this template
      const { data: documents, error: checkError } = await supabase
        .from('documents')
        .select('id')
        .eq('template_id', templateToDelete);

      if (checkError) throw checkError;

      // If documents are using this template, show a specific error message
      if (documents && documents.length > 0) {
        toast.error(
          `Cannot delete template because it is being used by ${documents.length} document(s). ` +
          `This is a temporary limitation. In the future, templates will be fully detached from documents after creation.`
        );
        setTemplateToDelete(null);
        setShowDeleteDialog(false);
        return;
      }

      // If no documents are using the template, proceed with deletion
      const { error } = await supabase
        .from('document_templates')
        .delete()
        .eq('id', templateToDelete);

      if (error) {
        // Check for foreign key constraint error
        if (error.code === '23503' && error.details?.includes('documents')) {
          toast.error(
            "Cannot delete template because it is being used by one or more documents. " +
            "This is a temporary limitation. In the future, templates will be fully detached from documents after creation."
          );
        } else {
          throw error;
        }
      } else {
        setTemplates(templates.filter(template => template.id !== templateToDelete));
        toast.success("Template deleted successfully");
      }
    } catch (error: any) {
      console.error('Error deleting template:', error);
      toast.error("Error deleting template: " + (error.message || "There was a problem removing the template."));
    } finally {
      setTemplateToDelete(null);
      setShowDeleteDialog(false);
    }
  };

  const handleDuplicateTemplate = async (templateId: string) => {
    try {
      // Get the template to duplicate
      const { data, error } = await supabase
        .from('document_templates')
        .select('*')
        .eq('id', templateId)
        .single();

      if (error) throw error;

      if (data) {
        // Get the user's entity ID to use as owner_id
        const { getUserEntity } = await import('@/core/api/supabase-compat');
        const userEntityData = await getUserEntity(user!.id);

        if (!userEntityData || !userEntityData.entity_id) {
          throw new Error('Could not find an entity associated with your user account. Please ensure your account is properly set up.');
        }

        // Create a new template with the same content
        const { error: insertError } = await supabase
          .from('document_templates')
          .insert({
            title: `${data.title} (Copy)`,
            description: (data as any).description || '',
            content: data.content,
            variables: data.variables,
            venue_id: venueId, // Keep venue_id for backward compatibility
            document_type: data.document_type || 'contract',
            owner_entity_id: userEntityData.entity_id
          });

        if (insertError) throw insertError;

        toast.success("Template duplicated successfully");
        fetchTemplates(); // Refresh the list
      }
    } catch (error) {
      console.error('Error duplicating template:', error);
      toast.error("Error duplicating template. There was a problem creating a copy of the template.");
    }
  };

  const confirmDelete = (templateId: string) => {
    setTemplateToDelete(templateId);
    setShowDeleteDialog(true);
  };

  // Calculate pagination
  const indexOfLastItem = currentPage * rowsPerPage;
  const indexOfFirstItem = indexOfLastItem - rowsPerPage;
  const currentItems = filteredTemplates.slice(indexOfFirstItem, indexOfLastItem);
  const pageCount = Math.ceil(filteredTemplates.length / rowsPerPage);

  const handlePageChange = (page: number) => {
    if (page > 0 && page <= pageCount) {
      setCurrentPage(page);
    }
  };

  const resetFilters = () => {
    setSearchQuery('');
    setTimeFilter('all');
    setTypeFilter('all');
    setDateRange({
      from: undefined,
      to: undefined
    });
    setCurrentPage(1);
  };

  // Determine user type for layout
  const { isVenue, isAgency } = useAuth();
  const layoutUserType = isVenue ? 'venue' : isAgency ? 'agency' : 'venue';

  return (
    <DashboardLayout userType={layoutUserType}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Document Templates</h1>

          </div>

          <Button onClick={() => navigate('/templates/new')} className="bg-stagecloud-black hover:bg-stagecloud-purple/90">
            <Plus className="mr-2 h-4 w-4" />
            New Template
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Your Templates</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-6">
              <div className="flex flex-wrap gap-3 items-center">
                <div className="relative flex-1 min-w-[200px]">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search templates..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                {/* Visibility filter removed as templates are now only visible to venue users */}

                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All types</SelectItem>
                    <SelectItem value="contract">Contract</SelectItem>
                    <SelectItem value="rider">Rider</SelectItem>
                    <SelectItem value="callsheet">Call Sheet</SelectItem>
                    <SelectItem value="invoice">Invoice</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={timeFilter} onValueChange={setTimeFilter}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Time" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All time</SelectItem>
                    <SelectItem value="recent">Last 7 days</SelectItem>
                    <SelectItem value="older">Older</SelectItem>
                  </SelectContent>
                </Select>

                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-[240px] justify-start text-left font-normal",
                        !dateRange && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRange?.from ? (
                        dateRange.to ? (
                          <>
                            {format(dateRange.from, "LLL dd, y")} -{" "}
                            {format(dateRange.to, "LLL dd, y")}
                          </>
                        ) : (
                          format(dateRange.from, "LLL dd, y")
                        )
                      ) : (
                        <span>Date range</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      initialFocus
                      mode="range"
                      defaultMonth={dateRange?.from}
                      selected={dateRange}
                      onSelect={setDateRange}
                      numberOfMonths={2}
                    />
                  </PopoverContent>
                </Popover>


                <Button
                  variant="outline"
                  size="icon"
                  onClick={resetFilters}
                  title="Reset filters"
                >
                  <RotateCcw className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Template Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Variables</TableHead>
                    <TableHead>Last Updated</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin text-gray-500 mx-auto" />
                        <p className="mt-2 text-sm text-muted-foreground">Loading templates...</p>
                      </TableCell>
                    </TableRow>
                  ) : currentItems.length > 0 ? (
                    currentItems.map((template) => (
                      <TableRow
                        key={template.id}
                        className="hover:bg-muted/50 transition-colors cursor-pointer"
                        onClick={() => navigate(`/templates/${template.id}`)}
                      >
                        <TableCell className="font-medium">
                          <div className="flex items-center">
                            <FileEdit className="h-4 w-4 mr-2 text-muted-foreground" />
                            <span>{template.title}</span>
                          </div>
                        </TableCell>
                        <TableCell className="capitalize">
                          {template.document_type || 'contract'}
                        </TableCell>
                        <TableCell className="max-w-xs truncate">
                          {template.description || 'No description'}
                        </TableCell>
                        <TableCell>
                          {template.variables ? template.variables.length : 0} variables
                        </TableCell>
                        {/* Visibility column removed */}
                        <TableCell>
                          {template.updated_at
                            ? format(new Date(template.updated_at), 'MMM d, yyyy')
                            : format(new Date(template.created_at), 'MMM d, yyyy')}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={(e) => e.stopPropagation()}
                              >
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation();
                                navigate(`/templates/${template.id}`);
                              }}>
                                <Eye className="mr-2 h-4 w-4" />
                                View
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation();
                                navigate(`/templates/${template.id}/edit`);
                              }}>
                                <Pencil className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation();
                                handleDuplicateTemplate(template.id);
                              }}>
                                <Copy className="mr-2 h-4 w-4" />
                                Duplicate
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.stopPropagation();
                                  confirmDelete(template.id);
                                }}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <FileText className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                        <h3 className="text-lg font-medium mb-2">No templates found</h3>
                        <p className="text-sm text-gray-500 mb-4">
                          {searchQuery || timeFilter !== 'all' || typeFilter !== 'all' || dateRange?.from
                            ? "No templates match your current filters"
                            : "Create your first document template to streamline your booking process"}
                        </p>
                        {!searchQuery && timeFilter === 'all' && typeFilter === 'all' && !dateRange?.from && (
                          <Button onClick={() => navigate('/templates/new')}>
                            <Plus className="mr-2 h-4 w-4" />
                            Create New Template
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            <div className="mt-6 flex flex-col sm:flex-row gap-4 items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Rows per page:</span>
                <Select
                  value={String(rowsPerPage)}
                  onValueChange={value => {
                    setRowsPerPage(parseInt(value));
                    setCurrentPage(1);
                  }}
                >
                  <SelectTrigger className="w-[80px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="15">15</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {filteredTemplates.length > rowsPerPage && (
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => handlePageChange(currentPage - 1)}
                        className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>

                    {Array.from({ length: Math.min(pageCount, 5) }, (_, i) => {
                      let pageNum: number;
                      if (pageCount <= 5) {
                        pageNum = i + 1;
                      } else if (currentPage <= 3) {
                        pageNum = i + 1;
                      } else if (currentPage >= pageCount - 2) {
                        pageNum = pageCount - 4 + i;
                      } else {
                        pageNum = currentPage - 2 + i;
                      }
                      return (
                        <PaginationItem key={i}>
                          <PaginationLink
                            onClick={() => handlePageChange(pageNum)}
                            isActive={pageNum === currentPage}
                          >
                            {pageNum}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    })}

                    <PaginationItem>
                      <PaginationNext
                        onClick={() => handlePageChange(currentPage + 1)}
                        className={currentPage === pageCount ? "pointer-events-none opacity-50" : "cursor-pointer"}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={handleDeleteTemplate}
        title="Delete Template"
        description="Are you sure you want to delete this template? This action cannot be undone and any documents using this template will be affected."
      />
    </DashboardLayout>
  );
};

export default TemplateList;
