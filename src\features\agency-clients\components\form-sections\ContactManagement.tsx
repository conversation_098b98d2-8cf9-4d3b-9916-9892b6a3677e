import { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Plus, Trash2, User, Mail, Phone, Briefcase, Edit2, Save, X } from 'lucide-react';
import type { CreateAgencyClientContactData, EditableContactData } from '../../types';

interface ContactManagementProps {
  clientType: 'company' | 'person';
  contacts: EditableContactData[];
  setContacts: (contacts: EditableContactData[]) => void;
}

const ContactManagement = ({
  clientType,
  contacts,
  setContacts
}: ContactManagementProps) => {
  const [newContact, setNewContact] = useState<CreateAgencyClientContactData>({
    name: '',
    email: '',
    phone: '',
    position: '',
    is_primary: false
  });

  const [editingContactId, setEditingContactId] = useState<string | null>(null);
  const [editingContact, setEditingContact] = useState<EditableContactData | null>(null);

  const addContact = () => {
    if (!newContact.name.trim()) return;

    // Filter out deleted contacts for counting
    const activeContacts = contacts.filter(c => !c.isDeleted);

    // If this is the first active contact, make it primary
    const isPrimary = activeContacts.length === 0 || newContact.is_primary;

    // If making this contact primary, remove primary from others
    const updatedContacts = isPrimary
      ? contacts.map(c => ({ ...c, is_primary: false }))
      : contacts;

    const newEditableContact: EditableContactData = {
      ...newContact,
      is_primary: isPrimary,
      isNew: true,
      isDeleted: false
    };

    setContacts([...updatedContacts, newEditableContact]);

    // Reset form
    setNewContact({
      name: '',
      email: '',
      phone: '',
      position: '',
      is_primary: false
    });
  };

  const removeContact = (index: number) => {
    const contactToRemove = contacts[index];

    if (contactToRemove.id && !contactToRemove.isNew) {
      // Mark existing contact for deletion
      const updatedContacts = contacts.map((contact, i) =>
        i === index ? { ...contact, isDeleted: true } : contact
      );

      // If we're deleting the primary contact, make another one primary
      if (contactToRemove.is_primary) {
        const activeContacts = updatedContacts.filter(c => !c.isDeleted);
        if (activeContacts.length > 0) {
          const firstActiveIndex = updatedContacts.findIndex(c => !c.isDeleted);
          if (firstActiveIndex !== -1) {
            updatedContacts[firstActiveIndex].is_primary = true;
          }
        }
      }

      setContacts(updatedContacts);
    } else {
      // Remove new contact completely
      const updatedContacts = contacts.filter((_, i) => i !== index);

      // If we removed the primary contact and there are others, make the first one primary
      if (contactToRemove.is_primary && updatedContacts.length > 0) {
        const activeContacts = updatedContacts.filter(c => !c.isDeleted);
        if (activeContacts.length > 0) {
          const firstActiveIndex = updatedContacts.findIndex(c => !c.isDeleted);
          if (firstActiveIndex !== -1) {
            updatedContacts[firstActiveIndex].is_primary = true;
          }
        }
      }

      setContacts(updatedContacts);
    }
  };

  const setPrimaryContact = (index: number) => {
    const updatedContacts = contacts.map((contact, i) => ({
      ...contact,
      is_primary: i === index
    }));
    setContacts(updatedContacts);
  };

  const startEditingContact = (index: number) => {
    const contact = contacts[index];
    setEditingContactId(contact.id || `new-${index}`);
    setEditingContact({ ...contact });
  };

  const saveEditingContact = () => {
    if (!editingContact || !editingContact.name.trim()) return;

    const updatedContacts = contacts.map(contact =>
      (contact.id === editingContactId || (!contact.id && !editingContactId?.startsWith('new-')))
        ? editingContact
        : contact
    );

    setContacts(updatedContacts);
    setEditingContactId(null);
    setEditingContact(null);
  };

  const cancelEditingContact = () => {
    setEditingContactId(null);
    setEditingContact(null);
  };

  // Only show contact management for company clients
  if (clientType !== 'company') {
    return null;
  }

  return (
    <>
      <h3 className="text-lg font-semibold">Contact Management</h3>
      <p className="text-sm text-gray-600 mb-4">
        Add contacts for this company. You can designate one as the primary contact.
      </p>

      {/* Existing Contacts */}
      {contacts.filter(c => !c.isDeleted).length > 0 && (
        <div className="space-y-3 mb-4">
          {contacts.map((contact, index) => {
            if (contact.isDeleted) return null;

            const isEditing = editingContactId === (contact.id || `new-${index}`);

            return (
              <Card key={contact.id || `new-${index}`} className="relative">
                <CardContent className="pt-4">
                  {isEditing ? (
                    // Edit mode
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="grid gap-2">
                          <Label htmlFor={`edit-name-${index}`}>Name *</Label>
                          <Input
                            id={`edit-name-${index}`}
                            value={editingContact?.name || ''}
                            onChange={(e) => setEditingContact(prev => prev ? { ...prev, name: e.target.value } : null)}
                            placeholder="Contact name"
                          />
                        </div>
                        <div className="grid gap-2">
                          <Label htmlFor={`edit-position-${index}`}>Position</Label>
                          <Input
                            id={`edit-position-${index}`}
                            value={editingContact?.position || ''}
                            onChange={(e) => setEditingContact(prev => prev ? { ...prev, position: e.target.value } : null)}
                            placeholder="Position/Title"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="grid gap-2">
                          <Label htmlFor={`edit-email-${index}`}>Email</Label>
                          <Input
                            id={`edit-email-${index}`}
                            type="email"
                            value={editingContact?.email || ''}
                            onChange={(e) => setEditingContact(prev => prev ? { ...prev, email: e.target.value } : null)}
                            placeholder="Email address"
                          />
                        </div>
                        <div className="grid gap-2">
                          <Label htmlFor={`edit-phone-${index}`}>Phone</Label>
                          <Input
                            id={`edit-phone-${index}`}
                            value={editingContact?.phone || ''}
                            onChange={(e) => setEditingContact(prev => prev ? { ...prev, phone: e.target.value } : null)}
                            placeholder="Phone number"
                          />
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={`edit-primary-${index}`}
                            checked={editingContact?.is_primary || false}
                            onCheckedChange={(checked) =>
                              setEditingContact(prev => prev ? { ...prev, is_primary: !!checked } : null)
                            }
                          />
                          <Label htmlFor={`edit-primary-${index}`} className="text-sm">
                            Primary contact
                          </Label>
                        </div>

                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={cancelEditingContact}
                          >
                            <X className="h-4 w-4 mr-1" />
                            Cancel
                          </Button>
                          <Button
                            variant="default"
                            size="sm"
                            onClick={saveEditingContact}
                            disabled={!editingContact?.name.trim()}
                          >
                            <Save className="h-4 w-4 mr-1" />
                            Save
                          </Button>
                        </div>
                      </div>
                    </div>
                  ) : (
                    // View mode
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <User className="h-4 w-4 text-gray-500" />
                          <span className="font-medium">{contact.name}</span>
                          {contact.is_primary && (
                            <Badge variant="default" className="text-xs">
                              Primary
                            </Badge>
                          )}
                          {contact.isNew && (
                            <Badge variant="secondary" className="text-xs">
                              New
                            </Badge>
                          )}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600">
                          {contact.email && (
                            <div className="flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              <span>{contact.email}</span>
                            </div>
                          )}
                          {contact.phone && (
                            <div className="flex items-center gap-1">
                              <Phone className="h-3 w-3" />
                              <span>{contact.phone}</span>
                            </div>
                          )}
                          {contact.position && (
                            <div className="flex items-center gap-1">
                              <Briefcase className="h-3 w-3" />
                              <span>{contact.position}</span>
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-2 ml-4">
                        {!contact.is_primary && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setPrimaryContact(index)}
                            className="text-xs"
                          >
                            Make Primary
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => startEditingContact(index)}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <Edit2 className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeContact(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Add New Contact Form */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Add New Contact</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="contactName">
                Contact Name *
              </Label>
              <Input
                id="contactName"
                placeholder="Enter contact name"
                value={newContact.name}
                onChange={(e) => setNewContact({ ...newContact, name: e.target.value })}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="contactPosition">
                Position/Title
              </Label>
              <Input
                id="contactPosition"
                placeholder="e.g., Manager, CEO"
                value={newContact.position}
                onChange={(e) => setNewContact({ ...newContact, position: e.target.value })}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="contactEmail">
                Email Address
              </Label>
              <Input
                id="contactEmail"
                type="email"
                placeholder="<EMAIL>"
                value={newContact.email}
                onChange={(e) => setNewContact({ ...newContact, email: e.target.value })}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="contactPhone">
                Phone Number
              </Label>
              <Input
                id="contactPhone"
                type="tel"
                placeholder="+****************"
                value={newContact.phone}
                onChange={(e) => setNewContact({ ...newContact, phone: e.target.value })}
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isPrimary"
              checked={newContact.is_primary}
              onCheckedChange={(checked) =>
                setNewContact({ ...newContact, is_primary: !!checked })
              }
            />
            <Label htmlFor="isPrimary" className="text-sm">
              Set as primary contact
            </Label>
          </div>

          <Button
            type="button"
            onClick={addContact}
            disabled={!newContact.name.trim()}
            className="w-full"
            variant="outline"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Contact
          </Button>
        </CardContent>
      </Card>
    </>
  );
};

export default ContactManagement;
