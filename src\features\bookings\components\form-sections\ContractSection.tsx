import { Label } from '@/components/ui/label';
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface ContractTemplate {
  id: string;
  title: string;
  content: string;
}

interface ContractSectionProps {
  createContract: boolean;
  setCreateContract: (create: boolean) => void;
  contractTemplates: ContractTemplate[];
  selectedTemplateId: string | null;
  setSelectedTemplateId: (id: string) => void;
}

const ContractSection = ({
  createContract,
  setCreateContract,
  contractTemplates,
  selectedTemplateId,
  setSelectedTemplateId
}: ContractSectionProps) => {
  return (
    <>
      <h3 className="text-lg font-semibold">Contract</h3>

      <div className="flex items-center space-x-2 mb-2">
        <Checkbox
          id="createContract"
          checked={createContract}
          onCheckedChange={(checked) => setCreateContract(!!checked)}
        />
        <Label htmlFor="createContract">
          Create a contract for this booking
        </Label>
      </div>

      {createContract && (
        <div className="grid gap-2 pl-6 border-l-2 border-gray-200">
          <Label htmlFor="contractTemplate">
            Select Contract Template
          </Label>

          {contractTemplates.length > 0 ? (
            <Select
              value={selectedTemplateId || ""}
              onValueChange={setSelectedTemplateId}
            >
              <SelectTrigger>
                <SelectValue placeholder="Choose a template" />
              </SelectTrigger>
              <SelectContent>
                {contractTemplates.map(template => (
                  <SelectItem key={template.id} value={template.id}>
                    {template.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <div className="text-sm text-muted-foreground p-3 bg-gray-50 rounded-md">
              No contract templates available. Please create a template first.
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default ContractSection;
