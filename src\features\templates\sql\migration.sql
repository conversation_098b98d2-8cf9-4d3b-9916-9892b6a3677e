-- Document Templates Migration
-- Run this SQL in your Supabase SQL Editor
-- This script migrates document_templates table to use entity-based ownership

-- Add owner_entity_id column to document_templates table
ALTER TABLE public.document_templates 
ADD COLUMN IF NOT EXISTS owner_entity_id UUID REFERENCES public.entities(id) ON DELETE CASCADE;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_document_templates_owner_entity_id ON public.document_templates(owner_entity_id);

-- Migrate existing data from owner_id to owner_entity_id
-- This assumes that owner_id currently contains user IDs and we need to find their associated entities
UPDATE public.document_templates 
SET owner_entity_id = (
    SELECT e.id 
    FROM public.entities e 
    INNER JOIN public.users u ON e.id = u.entity_id 
    WHERE u.id = document_templates.owner_id
    LIMIT 1
)
WHERE owner_entity_id IS NULL AND owner_id IS NOT NULL;

-- For any remaining templates without owner_entity_id, try to use venue_id if it exists
UPDATE public.document_templates 
SET owner_entity_id = venue_id
WHERE owner_entity_id IS NULL AND venue_id IS NOT NULL;

-- Make owner_entity_id NOT NULL after migration
-- Note: Only uncomment this after verifying all templates have been migrated
-- ALTER TABLE public.document_templates ALTER COLUMN owner_entity_id SET NOT NULL;

-- Grant permissions
GRANT ALL ON public.document_templates TO authenticated;

-- Migration completed successfully
SELECT 'Document Templates migration completed successfully!' as status;
