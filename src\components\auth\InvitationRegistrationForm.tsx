import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/core/api/supabase';
import { useToast } from '@/core';
import { getInvitationByToken, acceptInvitation } from '@/features/entities/api/invitationHelpers';
import { Loader2, AlertCircle, CheckCircle2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

const InvitationRegistrationForm = () => {
  const [searchParams] = useSearchParams();
  const invitationToken = searchParams.get('invitation');
  const navigate = useNavigate();
  const { toast } = useToast();

  const [loading, setLoading] = useState(true);
  const [registering, setRegistering] = useState(false);
  const [invitation, setInvitation] = useState<any>(null);
  const [invitationStatus, setInvitationStatus] = useState<'loading' | 'valid' | 'expired' | 'invalid'>('loading');

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [name, setName] = useState('');

  const [passwordError, setPasswordError] = useState('');
  const [confirmPasswordError, setConfirmPasswordError] = useState('');

  useEffect(() => {
    const fetchInvitation = async () => {
      if (!invitationToken) {
        setInvitationStatus('invalid');
        setLoading(false);
        return;
      }

      try {
        const invitationData = await getInvitationByToken(invitationToken);

        if (!invitationData) {
          setInvitationStatus('invalid');
        } else if (invitationData.status === 'expired') {
          setInvitationStatus('expired');
        } else {
          setInvitation(invitationData);
          setEmail(invitationData.email);
          setInvitationStatus('valid');
        }
      } catch (error) {
        console.error('Error fetching invitation:', error);
        setInvitationStatus('invalid');
      } finally {
        setLoading(false);
      }
    };

    fetchInvitation();
  }, [invitationToken]);

  const validatePassword = (password: string) => {
    if (password.length < 8) {
      setPasswordError('Password must be at least 8 characters long');
      return false;
    }
    setPasswordError('');
    return true;
  };

  const validateConfirmPassword = (password: string, confirmPassword: string) => {
    if (password !== confirmPassword) {
      setConfirmPasswordError('Passwords do not match');
      return false;
    }
    setConfirmPasswordError('');
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validatePassword(password) || !validateConfirmPassword(password, confirmPassword)) {
      return;
    }

    setRegistering(true);

    try {
      // Register the user with Supabase Auth
      // Important: Set the invitation_entity_id in user metadata to prevent entity creation
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
            // Set a flag to indicate this is an invitation registration
            is_invitation: true,
            // Store the entity ID and type to prevent duplicate entity creation
            invitation_entity_id: invitation.entity_id,
            invitation_entity_type: invitation.entities.entity_type,
          },
        },
      });

      if (authError) {
        throw new Error(authError.message);
      }

      if (!authData.user) {
        throw new Error('Failed to create user account');
      }

      // Create the user record in public.users table if it doesn't exist
      const { error: userError } = await supabase
        .from('users')
        .insert({
          id: authData.user.id,
          email: email.toLowerCase(),
          name: name,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (userError && !userError.message.includes('duplicate key')) {
        console.error('Error creating user record:', userError);
        // Continue anyway as the user might be created by a trigger
      }

      // Accept the invitation - this will create the entity_user relationship
      const { success, error } = await acceptInvitation(invitation.id, authData.user.id);

      if (!success) {
        throw new Error(error || 'Failed to accept invitation');
      }

      // Show success message
      toast({
        title: 'Registration successful',
        description: `You have been added to ${invitation.entities.name}`,
      });

      // Determine which dashboard to redirect to based on entity type
      const entityType = invitation.entities.entity_type;
      let dashboardUrl = '/';

      if (entityType === 'artist') {
        dashboardUrl = '/artist/dashboard';
      } else if (entityType === 'venue') {
        dashboardUrl = '/venue/dashboard';
      } else if (entityType === 'agency') {
        dashboardUrl = '/agency/dashboard';
      }

      // Redirect to the appropriate dashboard
      navigate(dashboardUrl);
    } catch (error: any) {
      console.error('Registration error:', error);
      toast({
        title: 'Registration failed',
        description: error.message || 'An error occurred during registration',
        variant: 'destructive',
      });
    } finally {
      setRegistering(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle>Checking Invitation</CardTitle>
            <CardDescription>Please wait while we verify your invitation</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center py-6">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (invitationStatus === 'invalid') {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle>Invalid Invitation</CardTitle>
            <CardDescription>This invitation link is invalid or has already been used</CardDescription>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>
                The invitation link you followed is not valid. Please contact the person who invited you for a new invitation.
              </AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button onClick={() => navigate('/login')}>Go to Login</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (invitationStatus === 'expired') {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle>Expired Invitation</CardTitle>
            <CardDescription>This invitation has expired</CardDescription>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Expired</AlertTitle>
              <AlertDescription>
                The invitation link you followed has expired. Please contact the person who invited you for a new invitation.
              </AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button onClick={() => navigate('/login')}>Go to Login</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="flex justify-center items-center min-h-screen">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle>Accept Invitation</CardTitle>
          <CardDescription>
            You've been invited to join {invitation?.entities?.name} as a {invitation?.role}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={email}
                disabled
                className="bg-gray-100"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="name">Your Name</Label>
              <Input
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  if (passwordError) validatePassword(e.target.value);
                  if (confirmPasswordError) validateConfirmPassword(e.target.value, confirmPassword);
                }}
                required
              />
              {passwordError && <p className="text-sm text-red-500">{passwordError}</p>}
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => {
                  setConfirmPassword(e.target.value);
                  if (confirmPasswordError) validateConfirmPassword(password, e.target.value);
                }}
                required
              />
              {confirmPasswordError && <p className="text-sm text-red-500">{confirmPasswordError}</p>}
            </div>
            <Button type="submit" className="w-full" disabled={registering}>
              {registering ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Account...
                </>
              ) : (
                'Accept Invitation'
              )}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex justify-center">
          <p className="text-sm text-gray-500">
            Already have an account? <a href="/login" className="text-blue-600 hover:underline">Log in</a>
          </p>
        </CardFooter>
      </Card>
    </div>
  );
};

export default InvitationRegistrationForm;
