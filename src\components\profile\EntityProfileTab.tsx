import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast, useIsMobile } from '@/core';
import { Building, MapPin, Edit, X, Check, Users, Plus, MoreVertical, Trash2, Loader2, Mail, Phone, User, Upload, Building2, ImageIcon, Music, Headphones, FileText, Link, Instagram, Facebook, Twitter, Youtube, PlusCircle, Trash } from 'lucide-react';
import { supabase } from '@/core/api/supabase';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import AgencyArtistsManager from '@/components/agency/AgencyArtistsManager';
import InviteUserModal from './InviteUserModal';

interface EntityUser {
  user_id: string;
  user_name: string;
  email: string;
  phone_number: string | null;
  role: string;
  is_primary: boolean;
}

interface EntityProfileTabProps {
  entityType: 'artist' | 'venue' | 'agency';
  entityData: {
    id: string;
    name: string;
    description: string | null;
    entity_type: string;
  };
  profileData: any; // This will be artist_profiles, venue_profiles, or agency_profiles data
  entityUsers: EntityUser[];
  onEntityUpdated: () => void;
}

const EntityProfileTab = ({
  entityType,
  entityData,
  profileData,
  entityUsers,
  onEntityUpdated
}: EntityProfileTabProps) => {
  const [editing, setEditing] = useState(false);
  const [saving, setSaving] = useState(false);
  const [editedEntity, setEditedEntity] = useState({
    name: entityData.name || '',
    description: entityData.description || '',
  });
  const isMobile = useIsMobile();

  // For artist images
  const [newImage, setNewImage] = useState<File | null>(null);
  const [uploadingImage, setUploadingImage] = useState(false);

  // For venue/agency
  const [profileImage, setProfileImage] = useState<File | null>(null);
  const [bannerImage, setBannerImage] = useState<File | null>(null);

  const getProfileImageUrl = () => {
    if (entityType === 'agency') {
      return profileData?.profile_image_url || null;
    } else if (entityType === 'venue') {
      return profileData?.logo_url || null;
    } else {
      // For artists, return the first image from the images array if it exists
      return profileData?.images && profileData.images.length > 0 ? profileData.images[0] : null;
    }
  };

  const getBannerImageUrl = () => {
    if (entityType === 'agency') {
      return profileData?.banner_image_url || null;
    } else if (entityType === 'venue') {
      return profileData?.banner_url || null;
    } else {
      return null; // Artists don't have banner images anymore
    }
  };

  const [profileImageUrl, setProfileImageUrl] = useState<string | null>(getProfileImageUrl());
  const [bannerImageUrl, setBannerImageUrl] = useState<string | null>(getBannerImageUrl());
  const [uploadingProfileImage, setUploadingProfileImage] = useState(false);
  const [uploadingBannerImage, setUploadingBannerImage] = useState(false);

  // For user invitations
  const [showInviteUserModal, setShowInviteUserModal] = useState(false);

  const [editedProfile, setEditedProfile] = useState(() => {
    if (entityType === 'artist') {
      return {
        genre: profileData?.genre || [],
        region: profileData?.region || '',
        images: profileData?.images || [],
        experiences: profileData?.experiences || '',
        about: profileData?.about || '',
        mixtapes: profileData?.mixtapes || [],
        technical_rider: profileData?.technical_rider || '',
        hospitality_rider: profileData?.hospitality_rider || '',
        social_links: profileData?.social_links || {}
      };
    } else if (entityType === 'venue') {
      return {
        venue_type: profileData?.venue_type || '',
        address: profileData?.address || '',
        company_name: profileData?.company_name || '',
        vat_number: profileData?.vat_number || '',
        invoice_address: profileData?.invoice_address || '',
        logo_url: profileData?.logo_url || null,
        banner_url: profileData?.banner_url || null,
        public_contact_info: profileData?.public_contact_info || {}
      };
    } else if (entityType === 'agency') {
      return {
        profile_image_url: profileData?.profile_image_url || null,
        banner_image_url: profileData?.banner_image_url || null,
        public_contact_info: profileData?.public_contact_info || {},
        company_name: profileData?.company_name || '',
        vat_number: profileData?.vat_number || '',
        invoice_address: profileData?.invoice_address || '',
        social_links: profileData?.social_links || {}
      };
    } else {
      return {};
    }
  });

  const { toast } = useToast();



  const handleEntityChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEditedEntity(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setEditedProfile(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePublicContactChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEditedProfile(prev => ({
      ...prev,
      public_contact_info: {
        ...prev.public_contact_info,
        [name]: value
      }
    }));
  };

  const handleProfileImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];

      if (entityType === 'artist') {
        setNewImage(file);
      } else {
        setProfileImage(file);
      }

      // Create a preview URL
      const previewUrl = URL.createObjectURL(file);
      setProfileImageUrl(previewUrl);

      // Check image dimensions
      const img = new Image();
      img.onload = () => {
        const width = img.width;
        const height = img.height;
        const aspectRatio = width / height;

        console.log(`Profile image dimensions: ${width}x${height}, aspect ratio: ${aspectRatio.toFixed(2)}`);

        // Ideal aspect ratio is 1:1
        if (aspectRatio < 0.8 || aspectRatio > 1.2) {
          toast({
            title: "Image dimensions notice",
            description: `For best results, use a square image (1:1 aspect ratio, like 400×400 pixels). Your image is ${width}×${height} (${aspectRatio.toFixed(2)}:1).`,
            variant: "default"
          });
        }
      };
      img.src = previewUrl;
    }
  };

  // Function to handle adding a new artist image
  const handleArtistImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      setNewImage(file);
      setUploadingImage(true);

      try {
        // Create a unique file name
        const fileExt = file.name.split('.').pop();
        const fileName = `${entityData.id}_image_${Date.now()}.${fileExt}`;
        const filePath = `entities/${fileName}`;

        // First check if the bucket exists
        const { data: buckets } = await supabase.storage.listBuckets();

        // Use the first available bucket or 'documents' as fallback
        const bucketName = buckets && buckets.length > 0 ? buckets[0].name : 'documents';

        // Upload the file to Supabase storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from(bucketName)
          .upload(filePath, file, {
            cacheControl: '3600',
            upsert: true
          });

        if (uploadError) {
          console.error('Error uploading image:', uploadError);
          toast({
            title: "Image upload failed",
            description: "Could not upload image. Please try again.",
            variant: "destructive"
          });
          return;
        }

        // Get the public URL for the file
        const { data: { publicUrl } } = supabase.storage
          .from(bucketName)
          .getPublicUrl(filePath);

        // Add the new image URL to the images array
        const updatedImages = [...editedProfile.images, publicUrl];
        setEditedProfile(prev => ({
          ...prev,
          images: updatedImages
        }));

        toast({
          title: "Image uploaded",
          description: "Image has been added to your profile",
          variant: "default"
        });
      } catch (error) {
        console.error('Exception in uploadImage:', error);
        toast({
          title: "Upload failed",
          description: "An error occurred while uploading the image",
          variant: "destructive"
        });
      } finally {
        setUploadingImage(false);
        setNewImage(null);
      }
    }
  };

  const handleBannerImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      setBannerImage(file);

      // Create a preview URL
      const previewUrl = URL.createObjectURL(file);
      setBannerImageUrl(previewUrl);

      // Check image dimensions
      const img = new Image();
      img.onload = () => {
        const width = img.width;
        const height = img.height;
        const aspectRatio = width / height;

        console.log(`Banner image dimensions: ${width}x${height}, aspect ratio: ${aspectRatio.toFixed(2)}`);

        // Ideal aspect ratio is 4:1
        if (aspectRatio < 3 || aspectRatio > 5) {
          toast({
            title: "Image dimensions notice",
            description: `For best results, use an image with a 4:1 aspect ratio (like 1200×300 pixels). Your image is ${width}×${height} (${aspectRatio.toFixed(2)}:1).`,
            variant: "default"
          });
        }
      };
      img.src = previewUrl;
    }
  };

  const uploadProfileImage = async (): Promise<string | null> => {
    if (!profileImage) {
      if (entityType === 'agency') {
        return editedProfile.profile_image_url;
      } else if (entityType === 'venue') {
        return editedProfile.logo_url;
      } else {
        return null; // Artists don't use this anymore
      }
    }

    setUploadingProfileImage(true);
    try {
      // Create a unique file name
      const fileExt = profileImage.name.split('.').pop();
      const fileName = `${entityData.id}_profile_${Date.now()}.${fileExt}`;
      const filePath = `entities/${fileName}`;

      console.log('Uploading profile image to path:', filePath);

      // First check if the bucket exists
      const { data: buckets } = await supabase.storage.listBuckets();
      console.log('Available buckets:', buckets);

      // Use the first available bucket or 'documents' as fallback
      const bucketName = buckets && buckets.length > 0 ? buckets[0].name : 'documents';
      console.log('Using bucket:', bucketName);

      // Upload the file to Supabase storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from(bucketName)
        .upload(filePath, profileImage, {
          cacheControl: '3600',
          upsert: true
        });

      if (uploadError) {
        console.error('Error uploading profile image:', uploadError);
        toast({
          title: "Image upload failed",
          description: "Could not upload profile image. Please try again.",
          variant: "destructive"
        });
        return null;
      }

      console.log('Upload successful:', uploadData);

      // Get the public URL for the file
      const { data: { publicUrl } } = supabase.storage
        .from(bucketName)
        .getPublicUrl(filePath);

      console.log('Public URL for profile image:', publicUrl);
      return publicUrl;
    } catch (error) {
      console.error('Exception in uploadProfileImage:', error);
      return null;
    } finally {
      setUploadingProfileImage(false);
    }
  };

  const uploadBannerImage = async (): Promise<string | null> => {
    if (!bannerImage) {
      if (entityType === 'artist' || entityType === 'agency') {
        return editedProfile.banner_image_url;
      } else {
        return editedProfile.banner_url;
      }
    }

    setUploadingBannerImage(true);
    try {
      // Create a unique file name
      const fileExt = bannerImage.name.split('.').pop();
      const fileName = `${entityData.id}_banner_${Date.now()}.${fileExt}`;
      const filePath = `entities/${fileName}`;

      console.log('Uploading banner image to path:', filePath);

      // First check if the bucket exists
      const { data: buckets } = await supabase.storage.listBuckets();

      // Use the first available bucket or 'documents' as fallback
      const bucketName = buckets && buckets.length > 0 ? buckets[0].name : 'documents';
      console.log('Using bucket for banner:', bucketName);

      // Upload the file to Supabase storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from(bucketName)
        .upload(filePath, bannerImage, {
          cacheControl: '3600',
          upsert: true
        });

      if (uploadError) {
        console.error('Error uploading banner image:', uploadError);
        toast({
          title: "Image upload failed",
          description: "Could not upload banner image. Please try again.",
          variant: "destructive"
        });
        return null;
      }

      console.log('Banner upload successful:', uploadData);

      // Get the public URL for the file
      const { data: { publicUrl } } = supabase.storage
        .from(bucketName)
        .getPublicUrl(filePath);

      console.log('Public URL for banner image:', publicUrl);
      return publicUrl;
    } catch (error) {
      console.error('Exception in uploadBannerImage:', error);
      return null;
    } finally {
      setUploadingBannerImage(false);
    }
  };

  const saveChanges = async () => {
    if (!entityData.id) return;
    setSaving(true);

    try {
      // For non-artist entities, upload images if they exist
      ('Starting image uploads...');
      let profileImageUrl = null;
      let bannerImageUrl = null;

      if (entityType !== 'artist') {
        profileImageUrl = await uploadProfileImage();
        console.log('Profile image upload result:', profileImageUrl);

        bannerImageUrl = await uploadBannerImage();
        console.log('Banner image upload result:', bannerImageUrl);
      }

      // Update entity record
      console.log('Updating entity record...');
      const { error: entityError } = await supabase
        .from('entities')
        .update({
          name: editedEntity.name,
          description: editedEntity.description,
          updated_at: new Date().toISOString()
        })
        .eq('id', entityData.id);

      if (entityError) {
        console.error('Error updating entity:', entityError);
        toast({
          title: "Update failed",
          description: "Could not update entity details",
          variant: "destructive"
        });
        setSaving(false);
        return;
      }

      // Prepare profile data with image URLs
      let profileData: any = {};

      if (entityType === 'artist') {
        profileData = {
          genre: editedProfile.genre,
          region: editedProfile.region,
          images: editedProfile.images,
          experiences: editedProfile.experiences,
          about: editedProfile.about,
          mixtapes: editedProfile.mixtapes,
          technical_rider: editedProfile.technical_rider,
          hospitality_rider: editedProfile.hospitality_rider,
          social_links: editedProfile.social_links,
          updated_at: new Date().toISOString()
        };
      } else if (entityType === 'venue') {
        profileData = {
          venue_type: editedProfile.venue_type,
          address: editedProfile.address,
          company_name: editedProfile.company_name,
          vat_number: editedProfile.vat_number,
          invoice_address: editedProfile.invoice_address,
          public_contact_info: editedProfile.public_contact_info,
          updated_at: new Date().toISOString()
        };

        // Only update image URLs if they were successfully uploaded
        if (profileImageUrl !== null) {
          profileData.logo_url = profileImageUrl;
        }
        if (bannerImageUrl !== null) {
          profileData.banner_url = bannerImageUrl;
        }
      } else if (entityType === 'agency') {
        profileData = {
          company_name: editedProfile.company_name,
          vat_number: editedProfile.vat_number,
          invoice_address: editedProfile.invoice_address,
          public_contact_info: editedProfile.public_contact_info,
          social_links: editedProfile.social_links,
          updated_at: new Date().toISOString()
        };

        // Only update image URLs if they were successfully uploaded
        if (profileImageUrl !== null) {
          profileData.profile_image_url = profileImageUrl;
        }
        if (bannerImageUrl !== null) {
          profileData.banner_image_url = bannerImageUrl;
        }
      }

      console.log('Updating profile with data:', profileData);

      // Update profile record based on entity type
      let profileError: any;
      if (entityType === 'artist') {
        const { error, data } = await supabase
          .from('artist_profiles')
          .update(profileData)
          .eq('entity_id', entityData.id)
          .select();
        profileError = error;
        console.log('Artist profile update result:', data, error);
      } else if (entityType === 'venue') {
        const { error, data } = await supabase
          .from('venue_profiles')
          .update(profileData)
          .eq('entity_id', entityData.id)
          .select();
        profileError = error;
        console.log('Venue profile update result:', data, error);
      } else if (entityType === 'agency') {
        try {
          // Use direct SQL for agency profiles since TypeScript doesn't know about this table yet
          // @ts-ignore - Ignore TypeScript errors for this operation
          const { error, data } = await supabase
            .from('agency_profiles')
            .update(profileData)
            .eq('entity_id', entityData.id)
            .select();
          profileError = error;
          console.log('Agency profile update result:', data, error);
        } catch (err) {
          console.error('Error updating agency profile:', err);
          profileError = err;
        }
      }

      if (profileError) {
        console.error('Error updating profile:', profileError);
        toast({
          title: "Update partially failed",
          description: "Entity was updated but profile details could not be saved",
          variant: "destructive"
        });
      } else {
        toast({
          title: "Success",
          description: "Entity and profile details updated successfully",
          variant: "default"
        });
        setEditing(false);
        onEntityUpdated();
      }
    } catch (error) {
      console.error('Exception in saveChanges:', error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setSaving(false);
    }
  };

  const cancelEdit = () => {
    setEditing(false);
    setEditedEntity({
      name: entityData.name || '',
      description: entityData.description || '',
    });

    // Reset image states
    setProfileImage(null);
    setBannerImage(null);
    setNewImage(null);
    setProfileImageUrl(getProfileImageUrl());
    setBannerImageUrl(getBannerImageUrl());

    if (entityType === 'artist') {
      setEditedProfile({
        genre: profileData?.genre || [],
        region: profileData?.region || '',
        images: profileData?.images || [],
        experiences: profileData?.experiences || '',
        about: profileData?.about || '',
        mixtapes: profileData?.mixtapes || [],
        technical_rider: profileData?.technical_rider || '',
        hospitality_rider: profileData?.hospitality_rider || '',
        social_links: profileData?.social_links || {}
      });
    } else if (entityType === 'venue') {
      setEditedProfile({
        venue_type: profileData?.venue_type || '',
        address: profileData?.address || '',
        company_name: profileData?.company_name || '',
        vat_number: profileData?.vat_number || '',
        invoice_address: profileData?.invoice_address || '',
        logo_url: profileData?.logo_url || null,
        banner_url: profileData?.banner_url || null,
        public_contact_info: profileData?.public_contact_info || {}
      });
    } else if (entityType === 'agency') {
      setEditedProfile({
        profile_image_url: profileData?.profile_image_url || null,
        banner_image_url: profileData?.banner_image_url || null,
        public_contact_info: profileData?.public_contact_info || {},
        company_name: profileData?.company_name || '',
        vat_number: profileData?.vat_number || '',
        invoice_address: profileData?.invoice_address || '',
        social_links: profileData?.social_links || {}
      });
    }
  };

  return (
    <div className="space-y-6">
      <Card className="overflow-hidden h-full flex flex-col">
        <CardHeader className="border-b flex flex-row items-center justify-between bg-transparent pb-3">
          <CardTitle className="flex items-center">
            <Building className="mr-2 h-5 w-5" />
            {entityType === 'artist' ? 'Artist Details' :
             entityType === 'venue' ? 'Venue Details' : 'Agency Details'}
          </CardTitle>
          <div className="flex space-x-2">
            {!editing ? (
              <Button variant="secondary" size="sm" onClick={() => setEditing(true)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={cancelEdit}>
                  <X className="h-4 w-4 mr-1" />
                  Cancel
                </Button>
                <Button
                  variant="default"
                  size="sm"
                  onClick={saveChanges}
                  disabled={saving}
                >
                  {saving ? (
                    <>
                      <span className="mr-1">Saving</span>
                      <Loader2 className="h-4 w-4 animate-spin" />
                    </>
                  ) : (
                    <>
                      <Check className="h-4 w-4 mr-1" />
                      Save
                    </>
                  )}
                </Button>
              </div>
            )}
          </div>
        </CardHeader>

        <CardContent className="p-6 space-y-6 flex-1">
          {/* Profile Images */}
          <div className="space-y-6">
            {entityType !== 'artist' && (
              <>
                {/* Banner Image - Only for non-artist entities */}
                <div className="space-y-1">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">Banner Image</span>
                  </div>
                  <div className="relative w-full max-w-full">
                    {/* Use AspectRatio to maintain consistent 4:1 ratio */}
                    <div className={`${isMobile ? 'w-full' : 'w-4/5'} ml-0 rounded-md overflow-hidden`}>
                      <AspectRatio ratio={4 / 1}>
                        {bannerImageUrl ? (
                          <img
                            src={bannerImageUrl}
                            alt="Banner"
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="flex flex-col items-center justify-center h-full bg-gray-100 w-full">
                            <ImageIcon className="h-8 w-8 text-gray-300 mb-2" />
                            <span className="text-gray-400 text-sm">No banner image</span>
                          </div>
                        )}
                      </AspectRatio>
                    </div>

                    {editing && (
                      <div className="absolute bottom-2 right-2 md:right-[calc(20%+8px)]">
                        <Label
                          htmlFor="banner-image"
                          className="cursor-pointer bg-white hover:bg-gray-100 px-3 py-2 rounded-md flex items-center text-sm transition-colors shadow-sm"
                        >
                          {uploadingBannerImage ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Uploading...
                            </>
                          ) : (
                            <>
                              <Upload className="h-4 w-4 mr-2" />
                              Upload Banner
                            </>
                          )}
                        </Label>
                        <Input
                          id="banner-image"
                          type="file"
                          className="hidden"
                          onChange={handleBannerImageChange}
                          accept="image/*"
                          disabled={uploadingBannerImage}
                        />
                      </div>
                    )}
                  </div>
                  <div className={`text-xs text-gray-400 italic ${isMobile ? 'w-full' : 'w-4/5'} ml-0`}>
                    For best results, use an image with a 4:1 aspect ratio (1200 × 300 pixels)
                  </div>
                </div>

                {/* Profile Image - Only for non-artist entities */}
                <div className="space-y-1">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">Profile Image</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="relative">
                      <div className="h-24 w-24 rounded-md overflow-hidden bg-gray-100">
                        {profileImageUrl ? (
                          <img
                            src={profileImageUrl}
                            alt="Profile"
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="flex flex-col items-center justify-center h-full">
                            <Building2 className="h-12 w-12 text-gray-300" />
                          </div>
                        )}
                      </div>

                      {editing && (
                        <div className="absolute -bottom-2 -right-2">
                          <Label
                            htmlFor="profile-image"
                            className="cursor-pointer bg-white hover:bg-gray-100 p-1.5 rounded-full flex items-center text-sm transition-colors shadow-sm"
                          >
                            {uploadingProfileImage ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Upload className="h-4 w-4" />
                            )}
                          </Label>
                          <Input
                            id="profile-image"
                            type="file"
                            className="hidden"
                            onChange={handleProfileImageChange}
                            accept="image/*"
                            disabled={uploadingProfileImage}
                          />
                        </div>
                      )}
                    </div>

                    <div>
                      <h3 className="font-medium text-lg">{entityData.name || 'Unnamed Entity'}</h3>
                      <p className="text-gray-500 text-sm capitalize">{entityType}</p>
                      <p className="text-xs text-gray-400 mt-1">Square image (1:1 ratio, 400 x 400 pixels) works best</p>
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* Artist Images Gallery - Only for artists */}
            {entityType === 'artist' && (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-500">Artist Images</span>
                  {editing && (
                    <Label
                      htmlFor="artist-image"
                      className="cursor-pointer bg-white hover:bg-gray-100 px-3 py-2 rounded-md flex items-center text-sm transition-colors shadow-sm border border-gray-200"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Add Image
                    </Label>
                  )}
                  <Input
                    id="artist-image"
                    type="file"
                    className="hidden"
                    onChange={handleArtistImageChange}
                    accept="image/*"
                  />
                </div>

                {/* Images grid */}
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {editedProfile.images && editedProfile.images.length > 0 ? (
                    editedProfile.images.map((imageUrl, index) => (
                      <div key={index} className="relative group">
                        <div className="aspect-square rounded-md overflow-hidden">
                          <img
                            src={imageUrl}
                            alt={`Artist image ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        {editing && (
                          <button
                            className="absolute top-2 right-2 bg-white/80 hover:bg-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                            onClick={() => {
                              const updatedImages = [...editedProfile.images];
                              updatedImages.splice(index, 1);
                              setEditedProfile({
                                ...editedProfile,
                                images: updatedImages
                              });
                            }}
                          >
                            <Trash2 className="h-4 w-4 text-red-500" />
                          </button>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="col-span-full flex flex-col items-center justify-center p-8 border-2 border-dashed border-gray-200 rounded-md">
                      <ImageIcon className="h-12 w-12 text-gray-300 mb-2" />
                      <p className="text-gray-500">No images added yet</p>
                      {editing && (
                        <p className="text-sm text-gray-400 mt-2">Click "Add Image" to upload artist photos</p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          <div className="grid gap-6">
            <div className="space-y-2">
              <div className="flex items-center">
                <Label htmlFor="name" className="text-sm font-medium text-gray-500">
                  {entityType === 'artist' ? 'Artist Name' :
                   entityType === 'venue' ? 'Venue Name' : 'Agency Name'}
                </Label>
              </div>
              {editing ? (
                <Input
                  id="name"
                  name="name"
                  value={editedEntity.name}
                  onChange={handleEntityChange}
                />
              ) : (
                <div className="font-medium">{entityData.name || 'Not specified'}</div>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex items-center">
                <Label htmlFor="description" className="text-sm font-medium text-gray-500">Description</Label>
              </div>
              {editing ? (
                <Textarea
                  id="description"
                  name="description"
                  value={editedEntity.description || ''}
                  onChange={handleEntityChange}
                  className="min-h-[100px]"
                />
              ) : (
                <div className="text-gray-700">{entityData.description || 'No description provided'}</div>
              )}
            </div>
          </div>

          {entityType === 'artist' ? (
            <div className="grid gap-6">
              {/* Basic Artist Info */}
              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="region" className="text-sm font-medium text-gray-500">Region</Label>
                </div>
                {editing ? (
                  <Input
                    id="region"
                    name="region"
                    value={editedProfile.region || ''}
                    onChange={handleProfileChange}
                  />
                ) : (
                  <div className="text-gray-700">{profileData?.region || 'Not specified'}</div>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="genre" className="text-sm font-medium text-gray-500">Genre</Label>
                </div>
                {editing ? (
                  <Input
                    id="genre"
                    name="genre"
                    value={Array.isArray(editedProfile.genre) ? editedProfile.genre.join(', ') : ''}
                    onChange={(e) => {
                      const genres = e.target.value.split(',').map(g => g.trim()).filter(Boolean);
                      setEditedProfile(prev => ({ ...prev, genre: genres } as typeof prev));
                    }}
                    placeholder="Enter genres separated by commas"
                  />
                ) : (
                  <div className="text-gray-700">
                    {Array.isArray(profileData?.genre) && profileData.genre.length > 0
                      ? profileData.genre.join(', ')
                      : 'Not specified'}
                  </div>
                )}
              </div>

              {/* About */}
              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="about" className="text-sm font-medium text-gray-500">About</Label>
                </div>
                {editing ? (
                  <Textarea
                    id="about"
                    name="about"
                    value={editedProfile.about || ''}
                    onChange={handleProfileChange}
                    className="min-h-[100px]"
                    placeholder="Write a bio or description about yourself as an artist"
                  />
                ) : (
                  <div className="text-gray-700 whitespace-pre-line">
                    {profileData?.about || 'No artist bio provided'}
                  </div>
                )}
              </div>

              {/* Experiences */}
              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="experiences" className="text-sm font-medium text-gray-500">Experiences</Label>
                </div>
                {editing ? (
                  <Textarea
                    id="experiences"
                    name="experiences"
                    value={editedProfile.experiences || ''}
                    onChange={handleProfileChange}
                    className="min-h-[100px]"
                    placeholder="List your past performances, residencies, or other relevant experience"
                  />
                ) : (
                  <div className="text-gray-700 whitespace-pre-line">
                    {profileData?.experiences || 'No experiences listed'}
                  </div>
                )}
              </div>

              {/* Technical Rider */}
              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="technical_rider" className="text-sm font-medium text-gray-500">Technical Rider</Label>
                </div>
                {editing ? (
                  <Textarea
                    id="technical_rider"
                    name="technical_rider"
                    value={editedProfile.technical_rider || ''}
                    onChange={handleProfileChange}
                    className="min-h-[100px]"
                    placeholder="List your technical requirements (equipment, setup, etc.)"
                  />
                ) : (
                  <div className="text-gray-700 whitespace-pre-line">
                    {profileData?.technical_rider || 'No technical rider provided'}
                  </div>
                )}
              </div>

              {/* Hospitality Rider */}
              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="hospitality_rider" className="text-sm font-medium text-gray-500">Hospitality Rider</Label>
                </div>
                {editing ? (
                  <Textarea
                    id="hospitality_rider"
                    name="hospitality_rider"
                    value={editedProfile.hospitality_rider || ''}
                    onChange={handleProfileChange}
                    className="min-h-[100px]"
                    placeholder="List your hospitality requirements (accommodation, food, drinks, etc.)"
                  />
                ) : (
                  <div className="text-gray-700 whitespace-pre-line">
                    {profileData?.hospitality_rider || 'No hospitality rider provided'}
                  </div>
                )}
              </div>

              {/* Social Links */}
              <div className="space-y-4">
                <div className="flex items-center">
                  <Label className="text-sm font-medium text-gray-500">Social Media Links</Label>
                </div>

                <div className="grid gap-4 pl-2 border-l-2 border-gray-100">
                  {/* Instagram */}
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Label htmlFor="instagram" className="text-sm font-medium text-gray-500 flex items-center">
                        Instagram
                      </Label>
                    </div>
                    {editing ? (
                      <Input
                        id="instagram"
                        name="instagram"
                        value={editedProfile.social_links?.instagram || ''}
                        onChange={(e) => {
                          setEditedProfile(prev => ({
                            ...prev,
                            social_links: {
                              ...prev.social_links,
                              instagram: e.target.value
                            }
                          }));
                        }}
                        placeholder="https://instagram.com/yourusername"
                      />
                    ) : (
                      <div className="text-gray-700">
                        {profileData?.social_links?.instagram ? (
                          <a href={profileData.social_links.instagram} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                            {profileData.social_links.instagram}
                          </a>
                        ) : (
                          'Not specified'
                        )}
                      </div>
                    )}
                  </div>

                  {/* SoundCloud */}
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Label htmlFor="soundcloud" className="text-sm font-medium text-gray-500 flex items-center">
                        SoundCloud
                      </Label>
                    </div>
                    {editing ? (
                      <Input
                        id="soundcloud"
                        name="soundcloud"
                        value={editedProfile.social_links?.soundcloud || ''}
                        onChange={(e) => {
                          setEditedProfile(prev => ({
                            ...prev,
                            social_links: {
                              ...prev.social_links,
                              soundcloud: e.target.value
                            }
                          }));
                        }}
                        placeholder="https://soundcloud.com/yourusername"
                      />
                    ) : (
                      <div className="text-gray-700">
                        {profileData?.social_links?.soundcloud ? (
                          <a href={profileData.social_links.soundcloud} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                            {profileData.social_links.soundcloud}
                          </a>
                        ) : (
                          'Not specified'
                        )}
                      </div>
                    )}
                  </div>

                  {/* Mixcloud */}
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Label htmlFor="mixcloud" className="text-sm font-medium text-gray-500 flex items-center">
                        Mixcloud
                      </Label>
                    </div>
                    {editing ? (
                      <Input
                        id="mixcloud"
                        name="mixcloud"
                        value={editedProfile.social_links?.mixcloud || ''}
                        onChange={(e) => {
                          setEditedProfile(prev => ({
                            ...prev,
                            social_links: {
                              ...prev.social_links,
                              mixcloud: e.target.value
                            }
                          }));
                        }}
                        placeholder="https://mixcloud.com/yourusername"
                      />
                    ) : (
                      <div className="text-gray-700">
                        {profileData?.social_links?.mixcloud ? (
                          <a href={profileData.social_links.mixcloud} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                            {profileData.social_links.mixcloud}
                          </a>
                        ) : (
                          'Not specified'
                        )}
                      </div>
                    )}
                  </div>

                  {/* Spotify */}
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Label htmlFor="spotify" className="text-sm font-medium text-gray-500 flex items-center">
                        Spotify
                      </Label>
                    </div>
                    {editing ? (
                      <Input
                        id="spotify"
                        name="spotify"
                        value={editedProfile.social_links?.spotify || ''}
                        onChange={(e) => {
                          setEditedProfile(prev => ({
                            ...prev,
                            social_links: {
                              ...prev.social_links,
                              spotify: e.target.value
                            }
                          }));
                        }}
                        placeholder="https://open.spotify.com/artist/..."
                      />
                    ) : (
                      <div className="text-gray-700">
                        {profileData?.social_links?.spotify ? (
                          <a href={profileData.social_links.spotify} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                            {profileData.social_links.spotify}
                          </a>
                        ) : (
                          'Not specified'
                        )}
                      </div>
                    )}
                  </div>

                  {/* Website */}
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <Label htmlFor="website" className="text-sm font-medium text-gray-500 flex items-center">
                        Website
                      </Label>
                    </div>
                    {editing ? (
                      <Input
                        id="website"
                        name="website"
                        value={editedProfile.social_links?.website || ''}
                        onChange={(e) => {
                          setEditedProfile(prev => ({
                            ...prev,
                            social_links: {
                              ...prev.social_links,
                              website: e.target.value
                            }
                          }));
                        }}
                        placeholder="https://yourwebsite.com"
                      />
                    ) : (
                      <div className="text-gray-700">
                        {profileData?.social_links?.website ? (
                          <a href={profileData.social_links.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                            {profileData.social_links.website}
                          </a>
                        ) : (
                          'Not specified'
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Mixtapes */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-sm font-medium text-gray-500">Mixtapes</Label>
                  {editing && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const currentMixtapes = Array.isArray(editedProfile.mixtapes) ? editedProfile.mixtapes : [];
                        setEditedProfile({
                          ...editedProfile,
                          mixtapes: [
                            ...currentMixtapes,
                            { title: '', url: '', platform: '', description: '' }
                          ]
                        });
                      }}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Mixtape
                    </Button>
                  )}
                </div>

                {editing ? (
                  <div className="space-y-4">
                    {Array.isArray(editedProfile.mixtapes) && editedProfile.mixtapes.length > 0 ? (
                      editedProfile.mixtapes.map((mixtape, index) => (
                        <div key={index} className="p-4 border border-gray-200 rounded-md relative">
                          <button
                            className="absolute top-2 right-2 text-gray-400 hover:text-red-500"
                            onClick={() => {
                              const updatedMixtapes = [...editedProfile.mixtapes];
                              updatedMixtapes.splice(index, 1);
                              setEditedProfile({
                                ...editedProfile,
                                mixtapes: updatedMixtapes
                              });
                            }}
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>

                          <div className="grid gap-3">
                            <div>
                              <Label htmlFor={`mixtape-title-${index}`} className="text-xs text-gray-500">Title</Label>
                              <Input
                                id={`mixtape-title-${index}`}
                                value={mixtape.title || ''}
                                onChange={(e) => {
                                  const updatedMixtapes = [...editedProfile.mixtapes];
                                  updatedMixtapes[index] = {
                                    ...updatedMixtapes[index],
                                    title: e.target.value
                                  };
                                  setEditedProfile({
                                    ...editedProfile,
                                    mixtapes: updatedMixtapes
                                  });
                                }}
                                placeholder="Mixtape title"
                              />
                            </div>

                            <div>
                              <Label htmlFor={`mixtape-url-${index}`} className="text-xs text-gray-500">URL</Label>
                              <Input
                                id={`mixtape-url-${index}`}
                                value={mixtape.url || ''}
                                onChange={(e) => {
                                  const updatedMixtapes = [...editedProfile.mixtapes];
                                  updatedMixtapes[index] = {
                                    ...updatedMixtapes[index],
                                    url: e.target.value
                                  };
                                  setEditedProfile({
                                    ...editedProfile,
                                    mixtapes: updatedMixtapes
                                  });
                                }}
                                placeholder="https://..."
                              />
                            </div>

                            <div>
                              <Label htmlFor={`mixtape-platform-${index}`} className="text-xs text-gray-500">Platform</Label>
                              <Input
                                id={`mixtape-platform-${index}`}
                                value={mixtape.platform || ''}
                                onChange={(e) => {
                                  const updatedMixtapes = [...editedProfile.mixtapes];
                                  updatedMixtapes[index] = {
                                    ...updatedMixtapes[index],
                                    platform: e.target.value
                                  };
                                  setEditedProfile({
                                    ...editedProfile,
                                    mixtapes: updatedMixtapes
                                  });
                                }}
                                placeholder="SoundCloud, Mixcloud, etc."
                              />
                            </div>

                            <div>
                              <Label htmlFor={`mixtape-description-${index}`} className="text-xs text-gray-500">Description</Label>
                              <Textarea
                                id={`mixtape-description-${index}`}
                                value={mixtape.description || ''}
                                onChange={(e) => {
                                  const updatedMixtapes = [...editedProfile.mixtapes];
                                  updatedMixtapes[index] = {
                                    ...updatedMixtapes[index],
                                    description: e.target.value
                                  };
                                  setEditedProfile({
                                    ...editedProfile,
                                    mixtapes: updatedMixtapes
                                  });
                                }}
                                placeholder="Short description of this mixtape"
                              />
                            </div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center p-6 border-2 border-dashed border-gray-200 rounded-md">
                        <p className="text-gray-500">No mixtapes added yet</p>
                        <p className="text-sm text-gray-400 mt-1">Click "Add Mixtape" to add your first mixtape</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {Array.isArray(profileData?.mixtapes) && profileData.mixtapes.length > 0 ? (
                      profileData.mixtapes.map((mixtape, index) => (
                        <div key={index} className="p-4 border border-gray-200 rounded-md">
                          <div className="flex justify-between items-start">
                            <div>
                              <h4 className="font-medium">{mixtape.title || 'Untitled Mixtape'}</h4>
                              <p className="text-sm text-gray-500">{mixtape.platform || 'Unknown platform'}</p>
                            </div>
                            {mixtape.url && (
                              <a
                                href={mixtape.url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:underline text-sm"
                              >
                                Listen
                              </a>
                            )}
                          </div>
                          {mixtape.description && (
                            <p className="mt-2 text-gray-700 text-sm">{mixtape.description}</p>
                          )}
                        </div>
                      ))
                    ) : (
                      <div className="text-center p-6 border-2 border-dashed border-gray-200 rounded-md">
                        <p className="text-gray-500">No mixtapes added yet</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          ) : entityType === 'venue' ? (
            <div className="grid gap-6">
              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="venue_type" className="text-sm font-medium text-gray-500">Venue Type</Label>
                </div>
                {editing ? (
                  <Input
                    id="venue_type"
                    name="venue_type"
                    value={editedProfile.venue_type || ''}
                    onChange={handleProfileChange}
                  />
                ) : (
                  <div className="text-gray-700">{profileData?.venue_type || 'Not specified'}</div>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="address" className="text-sm font-medium text-gray-500 flex items-center">
                    <MapPin className="h-4 w-4 mr-1" />
                    Address
                  </Label>
                </div>
                {editing ? (
                  <Textarea
                    id="address"
                    name="address"
                    value={editedProfile.address || ''}
                    onChange={handleProfileChange}
                    className="min-h-[100px]"
                  />
                ) : (
                  <div className="text-gray-700 whitespace-pre-line">{profileData?.address || 'Not specified'}</div>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="company_name" className="text-sm font-medium text-gray-500">Company Name</Label>
                </div>
                {editing ? (
                  <Input
                    id="company_name"
                    name="company_name"
                    value={editedProfile.company_name || ''}
                    onChange={handleProfileChange}
                  />
                ) : (
                  <div className="text-gray-700">{profileData?.company_name || 'Not specified'}</div>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="vat_number" className="text-sm font-medium text-gray-500">VAT Number</Label>
                </div>
                {editing ? (
                  <Input
                    id="vat_number"
                    name="vat_number"
                    value={editedProfile.vat_number || ''}
                    onChange={handleProfileChange}
                  />
                ) : (
                  <div className="text-gray-700">{profileData?.vat_number || 'Not specified'}</div>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="invoice_address" className="text-sm font-medium text-gray-500">Invoice Address</Label>
                </div>
                {editing ? (
                  <Textarea
                    id="invoice_address"
                    name="invoice_address"
                    value={editedProfile.invoice_address || ''}
                    onChange={handleProfileChange}
                    className="min-h-[100px]"
                  />
                ) : (
                  <div className="text-gray-700 whitespace-pre-line">{profileData?.invoice_address || 'Not specified'}</div>
                )}
              </div>
            </div>
          ) : (
            // Agency profile fields
            <div className="grid gap-6">
              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="company_name" className="text-sm font-medium text-gray-500">Company Name</Label>
                </div>
                {editing ? (
                  <Input
                    id="company_name"
                    name="company_name"
                    value={editedProfile.company_name || ''}
                    onChange={handleProfileChange}
                  />
                ) : (
                  <div className="text-gray-700">{profileData?.company_name || 'Not specified'}</div>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="vat_number" className="text-sm font-medium text-gray-500">VAT Number</Label>
                </div>
                {editing ? (
                  <Input
                    id="vat_number"
                    name="vat_number"
                    value={editedProfile.vat_number || ''}
                    onChange={handleProfileChange}
                  />
                ) : (
                  <div className="text-gray-700">{profileData?.vat_number || 'Not specified'}</div>
                )}
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <Label htmlFor="invoice_address" className="text-sm font-medium text-gray-500">Invoice Address</Label>
                </div>
                {editing ? (
                  <Textarea
                    id="invoice_address"
                    name="invoice_address"
                    value={editedProfile.invoice_address || ''}
                    onChange={handleProfileChange}
                    className="min-h-[100px]"
                  />
                ) : (
                  <div className="text-gray-700 whitespace-pre-line">{profileData?.invoice_address || 'Not specified'}</div>
                )}
              </div>
            </div>
          )}

          <div className="grid gap-6">
            <div className="space-y-4">
              <div className="flex items-center">
                <Label className="text-sm font-medium text-gray-500">Public Contact Information</Label>
              </div>

              <div className="grid gap-4 pl-2 border-l-2 border-gray-100">
                <div className="space-y-2">
                  <div className="flex items-center">
                    <Label htmlFor="public_email" className="text-sm font-medium text-gray-500 flex items-center">
                      <Mail className="h-4 w-4 mr-1" />
                      Public Email
                    </Label>
                  </div>
                  {editing ? (
                    <Input
                      id="public_email"
                      name="email"
                      value={editedProfile.public_contact_info?.email || ''}
                      onChange={handlePublicContactChange}
                    />
                  ) : (
                    <div className="text-gray-700">{profileData?.public_contact_info?.email || 'Not specified'}</div>
                  )}
                </div>

                <div className="space-y-2">
                  <div className="flex items-center">
                    <Label htmlFor="public_phone" className="text-sm font-medium text-gray-500 flex items-center">
                      <Phone className="h-4 w-4 mr-1" />
                      Public Phone
                    </Label>
                  </div>
                  {editing ? (
                    <Input
                      id="public_phone"
                      name="phone"
                      value={editedProfile.public_contact_info?.phone || ''}
                      onChange={handlePublicContactChange}
                    />
                  ) : (
                    <div className="text-gray-700">{profileData?.public_contact_info?.phone || 'Not specified'}</div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card className="overflow-hidden h-full flex flex-col">
        <CardHeader className="border-b flex flex-row items-center justify-between bg-transparent pb-3">
          <CardTitle className="flex items-center">
            <Users className="mr-2 h-5 w-5" />
            Related Users
          </CardTitle>
          <div className="flex space-x-2">
            <Button
              variant="secondary"
              size="sm"
              className="h-8"
              onClick={() => setShowInviteUserModal(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add User
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-0 flex-1 flex flex-col">
          <Table className="flex-1">
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Role</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {entityUsers.length > 0 ? (
                entityUsers.map((user) => (
                  <TableRow key={user.user_id} className="hover:bg-muted/50 transition-colors">
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-gray-500" />
                        <span className="font-medium">{user.user_name || 'Unnamed User'}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4 text-gray-500" />
                        <span>{user.email}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-gray-500" />
                        <span>{user.phone_number || 'Not specified'}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={user.is_primary ? "success" : "secondary"} className="capitalize">
                        {user.role}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem disabled>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Role
                          </DropdownMenuItem>
                          <DropdownMenuItem disabled>
                            <Trash2 className="h-4 w-4 mr-2" />
                            Remove User
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-12 border-2 border-dashed border-gray-200 m-6 rounded-md">
                    <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                    <h3 className="font-medium text-lg mb-1">No users associated with this entity</h3>
                    <p className="text-gray-500 mb-4">Add users to collaborate on this {entityType}</p>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Agency Artists Manager - Only show for agency profiles */}
      {entityType === 'agency' && (
        <div className="mt-6">
          <AgencyArtistsManager
            agencyId={entityData.id}
            onArtistsUpdated={onEntityUpdated}
          />
        </div>
      )}

      {/* Invite User Modal */}
      <InviteUserModal
        open={showInviteUserModal}
        onOpenChange={setShowInviteUserModal}
        entityId={entityData.id}
        entityName={entityData.name}
        entityType={entityType}
        onInvitationSent={() => {
          // Refresh the entity users list
          onEntityUpdated();
        }}
      />
    </div>
  );
};

export default EntityProfileTab;
