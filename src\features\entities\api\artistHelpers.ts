import { supabase } from '@/core/api/supabase';

/**
 * Get artist details for booking
 */
export const getArtistDetailsForBooking = async (artistId: string) => {
  try {
    // Get artist profile from entity ID
    const { data: artistDetails, error: artistError } = await supabase
      .from('artist_profiles')
      .select('*')
      .eq('entity_id', artistId)
      .limit(1);

    if (artistError) {
      console.error('Error fetching artist details for booking:', artistError);
      return null;
    }

    if (artistDetails && artistDetails.length > 0) {
      // Get the entity to get the name and description
      const { data: entityData, error: entityError } = await supabase
        .from('entities')
        .select('name, description')
        .eq('id', artistId)
        .limit(1);

      if (entityError) {
        console.error('Error fetching entity for artist details:', entityError);
      }

      // Get the first image from the images array to use as profile image
      const profileImageUrl = artistDetails[0].images &&
                             Array.isArray(artistDetails[0].images) &&
                             artistDetails[0].images.length > 0
                             ? artistDetails[0].images[0]
                             : null;

      return {
        artist_name: entityData && entityData.length > 0 ? entityData[0].name : 'Unknown Artist',
        profile_image_url: profileImageUrl,
        description: entityData && entityData.length > 0 ? entityData[0].description : null,
        genre: artistDetails[0].genre,
        region: artistDetails[0].region,
        social_links: artistDetails[0].social_links,
        images: artistDetails[0].images || [],
        experiences: artistDetails[0].experiences,
        about: artistDetails[0].about,
        mixtapes: artistDetails[0].mixtapes,
        technical_rider: artistDetails[0].technical_rider,
        hospitality_rider: artistDetails[0].hospitality_rider
      };
    }

    return null;
  } catch (error) {
    console.error('Exception in getArtistDetailsForBooking:', error);
    return null;
  }
};

/**
 * Get artist details
 */
export const getArtistDetails = async (entityId: string) => {
  try {
    // First try to get the artist details
    const { data: artistDetails, error: artistError } = await supabase
      .from('artist_profiles')
      .select('*')
      .eq('entity_id', entityId)
      .limit(1);

    if (artistError) {
      console.error('Error fetching artist details:', artistError);
      return null;
    }

    // Return the first item if it exists
    return artistDetails && artistDetails.length > 0 ? artistDetails[0] : null;
  } catch (error) {
    console.error('Exception in getArtistDetails:', error);
    return null;
  }
};
