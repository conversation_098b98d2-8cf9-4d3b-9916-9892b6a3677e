import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON>, useNavigate } from "react-router-dom";
import DashboardLayout from "@/components/layout/DashboardLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/contexts/AuthContext";
import { Badge } from "@/components/ui/badge";
import {
  MapPin, FileText, Clock, CheckCircle2, XCircle,
  Upload, Mail, Share2, FileText as FileTextIcon, AlertCircle, MoreVertical,
  Eye, UserCircle, MoreHorizontal, Edit, Trash2, ChevronDown, AlertTriangle,
  Paperclip, Phone, ArrowLeft, Loader2, Plus,
  Euro, FileUp, Instagram, Music, Headphones, Link as LinkIcon
} from 'lucide-react';
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { format, differenceInHours, differenceInMinutes } from "date-fns";
import { supabase, getBookingDocuments, getBookingDetails, getEntityUsers } from "@/core/api/supabase-compat";
import { toast } from 'sonner';

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import DocumentUploadModal from "@/components/documents/DocumentUploadModal";
import DocumentGenerateModal from "@/components/documents/DocumentGenerateModal";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import EditBookingModal from "@/components/bookings/EditBookingModal";
import { formatDateEU, formatTimeEU, formatCurrencyEU, calculateTotalPrice } from "@/core";
import { getDocumentStatus, getStatusBadgeVariant } from "@/core";

interface Document {
  id: string;
  name: string;
  file_url: string;
  created_at: string;
  document_type: string;
  status: string;
}

interface Person {
  id: string;
  name: string;
  email: string;
  phone?: string;
  role?: string;
}

interface EntityUser {
  user_id: string;
  user_name: string;
  email: string;
  phone_number: string | null;
  role: string;
  is_primary: boolean;
}

interface BookingDetails {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  duration: string;
  location: string;
  price: string | number;
  pricing_type: 'fixed' | 'hourly';
  status: "pending" | "confirmed" | "canceled" | "completed";
  payment_status: "paid" | "unpaid" | "partial";
  payment_due: string;
  artist: {
    id: string;
    name?: string;
    email?: string;
    profile_image_url?: string;
    description?: string;
    genre?: string[];
    artist_name?: string;
    banner_image_url?: string;
    region?: string;
    social_links?: {
      instagram?: string;
      spotify?: string;
      soundcloud?: string;
      mixcloud?: string;
      tiktok?: string;
    };
  };
  venue: {
    id: string;
    name?: string;
    address?: string;
    email?: string;
    description?: string;
    venue_type?: string;
    venue_name?: string;
    invoice_address?: string;
    company_name?: string;
    vat_number?: string;
    logo_url?: string;
    banner_url?: string;
  };
  documents: Document[];
  booking_start: string;
  booking_end: string;
  venue_id: string;
  artist_id: string;
  created_by?: string;
  people?: Person[];
  artistUsers?: EntityUser[];
}

const VenueBookingDetails = () => {
  const { id } = useParams<{ id: string; }>();
  const { profile } = useAuth();
  const navigate = useNavigate();

  const [booking, setBooking] = useState<BookingDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [errorMessage, setErrorMessage] = useState("");
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const [showDeleteBookingDialog, setShowDeleteBookingDialog] = useState(false);
  const [showDeleteDocumentDialog, setShowDeleteDocumentDialog] = useState(false);

  useEffect(() => {
    if (!id) return;
    fetchBookingDetails();
  }, [id, refreshKey]);

  const fetchBookingDetails = async () => {
    try {
      setIsLoading(true);
      setErrorMessage("");

      // Fetch booking details with artist and venue information, but without created_by join
      // Using left joins instead of inner joins to ensure we get the booking even if artist profiles are missing
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          id, title, description, booking_start, booking_end, status, price, pricing_type, notes, location, created_by,
          artist:artist_id (
            id,
            name,
            artist_profiles (
              images,
              genre,
              region,
              social_links,
              experiences,
              about,
              mixtapes,
              technical_rider,
              hospitality_rider
            )
          ),
          venue:venue_id (
            id,
            name,
            venue_profiles (
              logo_url,
              address,
              venue_type,
              invoice_address,
              company_name,
              vat_number
            )
          )
        `)
        .eq('id', id)
        .single();

      if (error) {
        console.error('Error fetching booking details:', error);
        setErrorMessage('Failed to load booking details. Please try again.');
        return;
      }

      if (!data) {
        setErrorMessage('Booking not found.');
        return;
      }

      // Fetch the creator user information separately
      let creatorInfo = { id: data.created_by, name: 'Unknown User', email: '' };

      try {
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('id, name, email')
          .eq('id', data.created_by)
          .single();

        if (!userError && userData) {
          creatorInfo = userData;
        }
      } catch (userErr) {
        console.error('Error fetching creator user details:', userErr);
        // Continue with default creator info
      }

      // Fetch artist users if artist exists
      let artistUsers = [];
      // Use type assertion to handle potential type errors
      const artistData = data.artist as any;
      if (artistData && artistData.id) {
        artistUsers = await getEntityUsers(artistData.id) || [];
      }

      // Fetch documents
      const { data: documentsData, error: documentsError } = await supabase
        .from('documents')
        .select('*')
        .eq('booking_id', id);

      if (documentsError) {
        console.error('Error fetching documents:', documentsError);
      }

      // Combine the booking data with the creator info and artist users
      const bookingWithDetails = {
        ...data,
        created_by: creatorInfo,
        artistUsers: artistUsers,
        documents: documentsData || []
      };

      setBooking(bookingWithDetails as any);
      setDocuments(documentsData || []);
    } catch (err) {
      console.error('Exception in fetchBookingDetails:', err);
      setErrorMessage('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const refreshData = () => {
    setRefreshKey(prev => prev + 1);
  };

  const handleStatusChange = async (newStatus: string) => {
    if (!booking) return;

    try {
      setIsSaving(true);

      const { error } = await supabase
        .from('bookings')
        .update({ status: newStatus })
        .eq('id', booking.id);

      if (error) throw error;

      // Update local state
      setBooking(prev => prev ? { ...prev, status: newStatus } : null);
      toast.success(`Booking status updated to ${newStatus}`);
    } catch (error) {
      console.error('Error updating booking status:', error);
      toast.error('Failed to update booking status');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteBooking = async () => {
    if (!booking) return;

    try {
      setIsDeleting(true);

      // Delete the booking
      const { error } = await supabase
        .from('bookings')
        .delete()
        .eq('id', booking.id);

      if (error) throw error;

      toast.success('Booking deleted successfully');
      navigate('/venue/bookings');
    } catch (error) {
      console.error('Error deleting booking:', error);
      toast.error('Failed to delete booking');
    } finally {
      setIsDeleting(false);
      setShowDeleteBookingDialog(false);
    }
  };

  const handleDeleteDocument = async () => {
    if (!documentToDelete) return;

    try {
      setIsDeleting(true);

      const { error } = await supabase
        .from('documents')
        .delete()
        .eq('id', documentToDelete);

      if (error) throw error;

      toast.success('Document deleted successfully');
      refreshData();
    } catch (error) {
      console.error('Error deleting document:', error);
      toast.error('Failed to delete document');
    } finally {
      setIsDeleting(false);
      setDocumentToDelete(null);
      setShowDeleteDocumentDialog(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-500 hover:bg-green-600 text-white';
      case 'pending': return 'bg-yellow-500 hover:bg-yellow-600 text-white';
      case 'cancelled': return 'bg-red-500 hover:bg-red-600 text-white';
      case 'completed': return 'bg-blue-500 hover:bg-blue-600 text-white';
      default: return 'bg-gray-500 hover:bg-gray-600 text-white';
    }
  };

  if (isLoading) {
    return (
      <DashboardLayout userType="venue">
        <div className="flex items-center justify-center h-[calc(100vh-200px)]">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          <span className="ml-2 text-gray-500">Loading booking details...</span>
        </div>
      </DashboardLayout>
    );
  }

  if (errorMessage || !booking) {
    return (
      <DashboardLayout userType="venue">
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{errorMessage || 'Booking not found'}</AlertDescription>
        </Alert>
        <Button asChild>
          <Link to="/venue/bookings">Back to Bookings</Link>
        </Button>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout userType="venue">
      <div className="space-y-6">
        <div className="space-y-4">
          {/* Top row with back button and booking info */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold tracking-tight">{booking.title}</h1>
                {/* <p className="text-muted-foreground">Booking ID: {booking.id}</p> */}
              </div>
            </div>
            {/* Desktop buttons - hidden on mobile */}
            <div className="hidden sm:flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button className={`${getStatusColor(booking.status)}`} disabled={isSaving}>
                    {isSaving ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : null}
                    {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                    <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => handleStatusChange('pending')}>
                    <Badge variant="warning" className="mr-2">Pending</Badge>
                    Set as Pending
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleStatusChange('confirmed')}>
                    <Badge variant="success" className="mr-2">Confirmed</Badge>
                    Set as Confirmed
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleStatusChange('completed')}>
                    <Badge variant="default" className="mr-2">Completed</Badge>
                    Set as Completed
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleStatusChange('cancelled')}>
                    <Badge variant="destructive" className="mr-2">Cancelled</Badge>
                    Set as Cancelled
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setShowEditModal(true)}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Booking
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setShowGenerateModal(true)}>
                    <FileText className="mr-2 h-4 w-4" />
                    Generate Document
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setShowUploadModal(true)}>
                    <Upload className="mr-2 h-4 w-4" />
                    Upload Document
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    onClick={() => setShowDeleteBookingDialog(true)}
                    className="text-red-600 focus:text-red-600"
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete Booking
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Mobile buttons - shown only on mobile */}
          <div className="flex sm:hidden items-center gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button className={`flex-1 ${getStatusColor(booking.status)}`} disabled={isSaving}>
                  {isSaving ? (
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  ) : null}
                  {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => handleStatusChange('pending')}>
                  <Badge variant="warning" className="mr-2">Pending</Badge>
                  Set as Pending
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusChange('confirmed')}>
                  <Badge variant="success" className="mr-2">Confirmed</Badge>
                  Set as Confirmed
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusChange('completed')}>
                  <Badge variant="default" className="mr-2">Completed</Badge>
                  Set as Completed
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusChange('cancelled')}>
                  <Badge variant="destructive" className="mr-2">Cancelled</Badge>
                  Set as Cancelled
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="flex-1">
                  <MoreVertical className="h-4 w-4 mr-2" />
                  Actions
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setShowEditModal(true)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Booking
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setShowGenerateModal(true)}>
                  <FileText className="mr-2 h-4 w-4" />
                  Generate Document
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setShowUploadModal(true)}>
                  <Upload className="mr-2 h-4 w-4" />
                  Upload Document
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setShowDeleteBookingDialog(true)}
                  className="text-red-600 focus:text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Booking
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Left column - Booking details */}
          <div className="md:col-span-2 space-y-6">
            <Card className="overflow-hidden">
              <CardHeader className="border-b bg-transparent">
                <CardTitle>Booking Details</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Date & Time</h3>
                      <div className="flex items-center mt-1">
                        <Clock className="h-4 w-4 text-gray-400 mr-2" />
                        <p>
                          {formatDateEU(new Date(booking.booking_start))} • {formatTimeEU(new Date(booking.booking_start))} - {formatTimeEU(new Date(booking.booking_end))}
                        </p>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Location</h3>
                      <div className="flex items-center mt-1">
                        <MapPin className="h-4 w-4 text-gray-400 mr-2" />
                        <p>{booking.location || 'No location specified'}</p>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Price</h3>
                      <div className="flex items-center mt-1">
                        <Euro className="h-4 w-4 text-gray-400 mr-2" />
                        <p>
                          {booking.pricing_type === 'hourly'
                            ? `${formatCurrencyEU(booking.price)} per hour (Total: ${formatCurrencyEU(calculateTotalPrice(booking.price, booking.pricing_type, booking.booking_start, booking.booking_end))})`
                            : formatCurrencyEU(booking.price)}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Created By</h3>
                      <div className="flex items-center mt-1">
                        <UserCircle className="h-4 w-4 text-gray-400 mr-2" />
                        <p>{booking.created_by?.name || 'Unknown'}</p>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-gray-500">Status</h3>
                      <div className="flex items-center mt-1">
                        <Badge variant={getStatusBadgeVariant(booking.status)}>
                          {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>

                {booking.description && (
                  <div className="mt-6">
                    <h3 className="text-sm font-medium text-gray-500 mb-2">Description</h3>
                    <p className="text-gray-700 whitespace-pre-wrap">{booking.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <CardHeader className="border-b bg-transparent flex flex-row items-center justify-between">
                <CardTitle>Documents</CardTitle>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm" onClick={() => setShowUploadModal(true)}>
                    <Upload className="h-4 w-4 mr-2" />
                    Upload
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => setShowGenerateModal(true)}>
                    <FileUp className="h-4 w-4 mr-2" />
                    Generate
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-0">
                {documents.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-8 text-center">
                    <FileText className="h-8 w-8 text-gray-300 mb-2" />
                    <h3 className="text-sm font-medium text-gray-900">No documents yet</h3>
                    <p className="text-sm text-gray-500 mt-1 max-w-sm">
                      Upload or generate documents for this booking.
                    </p>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {documents.map((doc) => (
                        <TableRow key={doc.id}>
                          <TableCell className="font-medium">{doc.name}</TableCell>
                          <TableCell>
                            <Badge variant="outline">
                              {doc.document_type.charAt(0).toUpperCase() + doc.document_type.slice(1)}
                            </Badge>
                          </TableCell>
                          <TableCell>{format(new Date(doc.created_at), 'MMM d, yyyy')}</TableCell>
                          <TableCell>
                            <Badge variant={getStatusBadgeVariant(getDocumentStatus(doc))}>
                              {getDocumentStatus(doc)}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => navigate(`/documents/${doc.id}`)}>
                                  <Eye className="mr-2 h-4 w-4" />
                                  <span>View</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => {
                                  setDocumentToDelete(doc.id);
                                  setShowDeleteDocumentDialog(true);
                                }}>
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  <span>Delete</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Right column - Artist profile and contacts */}
          <div className="space-y-6">
            <Card className="overflow-hidden">
              <CardHeader className="border-b bg-transparent">
                <CardTitle>Artist Profile</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                {booking.artist ? (
                  <div className="flex flex-col space-y-6">
                    <div className="flex items-center space-x-4">
                      {/* Use type assertion to handle the updated data structure */}
                      {(() => {
                        const artist = booking.artist as any;
                        const artistProfiles = artist.artist_profiles || {};
                        // Get the first image from the images array if it exists
                        const images = artistProfiles.images || [];
                        const profileImage = images.length > 0 ? images[0] : null;
                        const genre = artistProfiles.genre || artist.genre;
                        const region = artistProfiles.region || artist.region;
                        const socialLinks = artistProfiles.social_links || {};
                        const about = artistProfiles.about;
                        const experiences = artistProfiles.experiences;
                        const mixtapes = artistProfiles.mixtapes || [];
                        const technicalRider = artistProfiles.technical_rider;
                        const hospitalityRider = artistProfiles.hospitality_rider;

                        return (
                          <>
                            <Avatar className="h-16 w-16 border-2 border-white shadow-md flex-shrink-0">
                              {profileImage ? (
                                <AvatarImage src={profileImage} alt={artist.name} />
                              ) : (
                                <AvatarFallback className="bg-blue-100 text-blue-800 text-lg font-semibold">
                                  {artist.name ? artist.name.charAt(0) : 'A'}
                                </AvatarFallback>
                              )}
                            </Avatar>
                            <div>
                              <h3 className="font-medium text-lg">{artist.name || 'Unknown Artist'}</h3>
                              <div className="flex flex-wrap gap-2 mt-1">
                                {genre && genre.length > 0 && (
                                  <Badge variant="outline" className="text-xs">
                                    {Array.isArray(genre) ? genre.join(', ') : genre}
                                  </Badge>
                                )}
                                {region && (
                                  <Badge variant="outline" className="text-xs">
                                    {region}
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </>
                        );
                      })()}
                    </div>

                    {/* Artist About */}
                    {(() => {
                      const artist = booking.artist as any;
                      const artistProfiles = artist.artist_profiles || {};
                      const about = artistProfiles.about;

                      if (about) {
                        return (
                          <div>
                            <h4 className="text-sm font-medium text-gray-500 mb-1">About</h4>
                            <p className="text-sm text-gray-700 whitespace-pre-line">{about}</p>
                          </div>
                        );
                      }
                      return null;
                    })()}

                    {/* Artist Social Links */}
                    {(() => {
                      const artist = booking.artist as any;
                      const artistProfiles = artist.artist_profiles || {};
                      const socialLinks = artistProfiles.social_links || {};

                      if (Object.keys(socialLinks).length > 0) {
                        return (
                          <div>
                            <h4 className="text-sm font-medium text-gray-500 mb-2">Social Links</h4>
                            <div className="flex flex-wrap gap-2">
                              {socialLinks.instagram && (
                                <a
                                  href={socialLinks.instagram}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-gray-500 hover:text-gray-700"
                                >
                                  <Badge variant="secondary" className="flex items-center gap-1">
                                    <Instagram className="h-3 w-3" />
                                    <span>Instagram</span>
                                  </Badge>
                                </a>
                              )}
                              {socialLinks.soundcloud && (
                                <a
                                  href={socialLinks.soundcloud}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-gray-500 hover:text-gray-700"
                                >
                                  <Badge variant="secondary" className="flex items-center gap-1">
                                    <Music className="h-3 w-3" />
                                    <span>SoundCloud</span>
                                  </Badge>
                                </a>
                              )}
                              {socialLinks.mixcloud && (
                                <a
                                  href={socialLinks.mixcloud}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-gray-500 hover:text-gray-700"
                                >
                                  <Badge variant="secondary" className="flex items-center gap-1">
                                    <Headphones className="h-3 w-3" />
                                    <span>Mixcloud</span>
                                  </Badge>
                                </a>
                              )}
                              {socialLinks.spotify && (
                                <a
                                  href={socialLinks.spotify}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-gray-500 hover:text-gray-700"
                                >
                                  <Badge variant="secondary" className="flex items-center gap-1">
                                    <Music className="h-3 w-3" />
                                    <span>Spotify</span>
                                  </Badge>
                                </a>
                              )}
                              {socialLinks.website && (
                                <a
                                  href={socialLinks.website}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-gray-500 hover:text-gray-700"
                                >
                                  <Badge variant="secondary" className="flex items-center gap-1">
                                    <LinkIcon className="h-3 w-3" />
                                    <span>Website</span>
                                  </Badge>
                                </a>
                              )}
                            </div>
                          </div>
                        );
                      }
                      return null;
                    })()}
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <UserCircle className="h-8 w-8 text-gray-300 mx-auto mb-1" />
                    <p className="text-gray-500 text-sm">No artist information available</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <CardHeader className="border-b bg-transparent">
                <CardTitle>Artist Contacts</CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                {booking.artistUsers && booking.artistUsers.length > 0 ? (
                  <div className="divide-y">
                    {booking.artistUsers.map((user) => (
                      <div key={user.user_id} className="p-4 flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            {user.profile_image_url ? (
                              <AvatarImage src={user.profile_image_url} alt={user.name} />
                            ) : (
                              <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                            )}
                          </Avatar>
                          <div>
                            <p className="font-medium">
                              {user.name}
                              {user.is_primary && (
                                <Badge variant="outline" className="ml-2 text-xs">Primary</Badge>
                              )}
                            </p>
                            <p className="text-sm text-gray-500">{user.role || 'Member'}</p>
                          </div>
                        </div>
                        <div className="flex space-x-2">
                          {user.email && (
                            <Button variant="ghost" size="icon" asChild>
                              <a href={`mailto:${user.email}`} title="Email">
                                <Mail className="h-4 w-4" />
                              </a>
                            </Button>
                          )}
                          {user.phone_number && (
                            <Button variant="ghost" size="icon" asChild>
                              <a href={`tel:${user.phone_number}`} title="Call">
                                <Phone className="h-4 w-4" />
                              </a>
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <UserCircle className="h-8 w-8 text-gray-300 mx-auto mb-1" />
                    <p className="text-gray-500 text-sm">No contacts available</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Modals */}
      <DeleteConfirmationDialog
        open={showDeleteBookingDialog}
        onClose={() => setShowDeleteBookingDialog(false)}
        onConfirm={handleDeleteBooking}
        title="Delete Booking"
        description="Are you sure you want to delete this booking? This action cannot be undone."
        isLoading={isDeleting}
      />

      <DeleteConfirmationDialog
        open={showDeleteDocumentDialog}
        onClose={() => setShowDeleteDocumentDialog(false)}
        onConfirm={handleDeleteDocument}
        title="Delete Document"
        description="Are you sure you want to delete this document? This action cannot be undone."
        isLoading={isDeleting}
      />

      {showUploadModal && (
        <DocumentUploadModal
          open={showUploadModal}
          onClose={() => setShowUploadModal(false)}
          bookingId={booking.id}
          onSuccess={refreshData}
        />
      )}

      {showGenerateModal && (
        <DocumentGenerateModal
          open={showGenerateModal}
          onClose={() => setShowGenerateModal(false)}
          bookingId={booking.id}
          onSuccess={refreshData}
        />
      )}

      {showEditModal && (
        <EditBookingModal
          open={showEditModal}
          onClose={() => setShowEditModal(false)}
          booking={booking}
          userType="venue"
          onBookingUpdated={refreshData}
        />
      )}
    </DashboardLayout>
  );
};

export default VenueBookingDetails;
