
import React, { useState } from 'react';
import { supabase } from '@/core/api/supabase';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';

const DemoAccounts = () => {
  const [loading, setLoading] = useState(false);
  const [created, setCreated] = useState(false);
  const navigate = useNavigate();

  const createDemoAccounts = async () => {
    setLoading(true);
    try {
      // Check if demo accounts already exist to prevent duplicates
      const { data: existingArtistData } = await supabase
        .from('profiles')
        .select('email')
        .eq('email', '<EMAIL>')
        .single();

      if (existingArtistData) {
        toast.info('Demo accounts already exist!');
        setCreated(true);
        setLoading(false);
        return;
      }

      // Create artist demo account
      const { data: artistData, error: artistError } = await supabase.auth.signUp({
        email: '<EMAIL>',
        password: 'demo123456',
        options: {
          data: {
            name: 'Demo Artist',
            user_type: 'artist',
            artist_name: 'DJ Demo'
          }
        }
      });

      ('Artist signup attempt:', { artistData, artistError });

      if (artistError) {
        console.error("Artist account creation error details:", artistError);
        toast.error(`Error creating artist account: ${artistError.message}`);
        setLoading(false);
        return;
      }

      // Wait a moment to ensure the first account is processed
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Continue with venue account only if artist account succeeds
      const { data: venueData, error: venueError } = await supabase.auth.signUp({
        email: '<EMAIL>',
        password: 'demo123456',
        options: {
          data: {
            name: 'Demo Venue Manager',
            user_type: 'venue',
            venue_name: 'Club Demo'
          }
        }
      });

      ('Venue signup attempt:', { venueData, venueError });

      if (venueError) {
        console.error("Venue account creation error details:", venueError);
        toast.error(`Error creating venue account: ${venueError.message}`);
        setLoading(false);
        return;
      }

      toast.success('Demo accounts created successfully!');
      setCreated(true);
    } catch (error: any) {
      console.error("General error creating accounts:", error);
      toast.error(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <Card className="w-[350px]">
        <CardHeader>
          <CardTitle>Demo Accounts</CardTitle>
          <CardDescription>
            Create demo accounts for testing
          </CardDescription>
        </CardHeader>
        <CardContent>
          {created ? (
            <div className="space-y-4">
              <div className="border p-4 rounded-md">
                <h3 className="font-medium">Artist Account</h3>
                <p>Email: <EMAIL></p>
                <p>Password: demo123456</p>
              </div>
              <div className="border p-4 rounded-md">
                <h3 className="font-medium">Venue Account</h3>
                <p>Email: <EMAIL></p>
                <p>Password: demo123456</p>
              </div>
            </div>
          ) : (
            <p>Click below to create demo accounts for both artist and venue users.</p>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button onClick={() => navigate('/login')}>
            Back to Login
          </Button>
          {!created && (
            <Button
              onClick={createDemoAccounts}
              disabled={loading}
            >
              {loading ? "Creating..." : "Create Demo Accounts"}
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
};

export default DemoAccounts;
