import { Node, mergeAttributes } from '@tiptap/core';

export interface VariableOptions {
  HTMLAttributes: Record<string, any>;
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    variable: {
      /**
       * Add a variable node
       */
      insertVariable: (options: { id: string; name: string; label: string }) => ReturnType;
    };
  }
}

export const Variable = Node.create<VariableOptions>({
  name: 'variable',
  
  group: 'inline',
  
  inline: true,
  
  selectable: true,
  
  atom: true,
  
  addAttributes() {
    return {
      id: {
        default: null,
      },
      name: {
        default: null,
      },
      label: {
        default: null,
      },
    };
  },
  
  parseHTML() {
    return [
      {
        tag: 'span.variable',
        getAttrs: (element) => {
          if (typeof element === 'string') {
            return false;
          }
          
          const htmlElement = element as HTMLElement;
          
          return {
            id: htmlElement.getAttribute('data-variable-id'),
            name: htmlElement.getAttribute('data-variable-name'),
            label: htmlElement.textContent,
          };
        },
      },
    ];
  },
  
  renderHTML({ HTMLAttributes }) {
    return [
      'span',
      mergeAttributes(
        { class: 'variable', 'data-variable-id': HTMLAttributes.id, 'data-variable-name': HTMLAttributes.name },
        this.options.HTMLAttributes
      ),
      HTMLAttributes.label,
    ];
  },
  
  addCommands() {
    return {
      insertVariable:
        (options) =>
        ({ commands }) => {
          return commands.insertContent({
            type: this.name,
            attrs: {
              id: options.id,
              name: options.name,
              label: options.label,
            },
          });
        },
    };
  },
});

export default Variable;
