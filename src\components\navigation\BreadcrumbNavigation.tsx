
import { useLocation } from 'react-router-dom';
import {
  B<PERSON><PERSON><PERSON>b,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
  BreadcrumbEllipsis,
} from '@/components/ui/breadcrumb';
import { HomeIcon } from 'lucide-react';
import { useMemo, useState, useEffect } from 'react';
import { useIsMobile } from '@/core';
import { supabase } from '@/core/api/supabase';

interface BreadcrumbNavigationProps {
  userType: 'venue' | 'artist' | 'agency';
}

const BreadcrumbNavigation = ({ userType }: BreadcrumbNavigationProps) => {
  const location = useLocation();
  const isMobile = useIsMobile();

  // State for storing entity names
  const [entityNames, setEntityNames] = useState<Record<string, string>>({});

  // Extract IDs from the current path
  const pathParts = location.pathname.split('/').filter(Boolean);

  // Function to fetch entity names based on type and ID
  const fetchEntityName = async (type: string, id: string): Promise<string> => {
    try {
      switch (type) {
        case 'booking': {
          const { data, error } = await supabase
            .from('bookings')
            .select('title')
            .eq('id', id)
            .single();

          if (error) {
            console.error('Error fetching booking name:', error);
            return id;
          }
          return data?.title || id;
        }

        case 'client': {
          const { data, error } = await supabase
            .from('agency_clients')
            .select('name')
            .eq('id', id)
            .single();

          if (error) {
            console.error('Error fetching client name:', error);
            return id;
          }
          return data?.name || id;
        }

        case 'document': {
          const { data, error } = await supabase
            .from('documents')
            .select('title')
            .eq('id', id)
            .single();

          if (error) {
            console.error('Error fetching document name:', error);
            return id;
          }
          return data?.title || id;
        }

        case 'template': {
          const { data, error } = await supabase
            .from('document_templates')
            .select('title')
            .eq('id', id)
            .single();

          if (error) {
            console.error('Error fetching template name:', error);
            return id;
          }
          return data?.title || id;
        }

        default:
          return id;
      }
    } catch (error) {
      console.error(`Error fetching ${type} name:`, error);
      return id; // Fallback to ID if fetch fails
    }
  };

  // Effect to fetch names for IDs in the current path
  useEffect(() => {
    const fetchNames = async () => {
      const idsToFetch: Array<{ type: string; id: string }> = [];

      // Check for booking IDs
      if (pathParts.includes('bookings') && pathParts.length > pathParts.indexOf('bookings') + 1) {
        const bookingId = pathParts[pathParts.indexOf('bookings') + 1];
        if (bookingId && !entityNames[`booking-${bookingId}`]) {
          idsToFetch.push({ type: 'booking', id: bookingId });
        }
      }

      // Check for client IDs
      if (pathParts.includes('clients') && pathParts.length > pathParts.indexOf('clients') + 1) {
        const clientId = pathParts[pathParts.indexOf('clients') + 1];
        if (clientId && !entityNames[`client-${clientId}`]) {
          idsToFetch.push({ type: 'client', id: clientId });
        }
      }

      // Check for document IDs (both /documents/{id} and /{userType}/paperwork/{id})
      if (pathParts.includes('documents') && pathParts.length > pathParts.indexOf('documents') + 1) {
        const documentId = pathParts[pathParts.indexOf('documents') + 1];
        if (documentId && !entityNames[`document-${documentId}`]) {
          idsToFetch.push({ type: 'document', id: documentId });
        }
      }
      if (pathParts.includes('paperwork') && pathParts.length > pathParts.indexOf('paperwork') + 1) {
        const documentId = pathParts[pathParts.indexOf('paperwork') + 1];
        if (documentId && !entityNames[`document-${documentId}`]) {
          idsToFetch.push({ type: 'document', id: documentId });
        }
      }

      // Check for template IDs
      if (pathParts.includes('templates') && pathParts.length > pathParts.indexOf('templates') + 1) {
        const templateId = pathParts[pathParts.indexOf('templates') + 1];
        if (templateId && templateId !== 'new' && !entityNames[`template-${templateId}`]) {
          idsToFetch.push({ type: 'template', id: templateId });
        }
      }

      // Fetch all names concurrently
      if (idsToFetch.length > 0) {
        const namePromises = idsToFetch.map(async ({ type, id }) => {
          const name = await fetchEntityName(type, id);
          return { key: `${type}-${id}`, name };
        });

        const results = await Promise.all(namePromises);
        const newNames: Record<string, string> = {};

        results.forEach(({ key, name }) => {
          newNames[key] = name;
        });

        setEntityNames(prev => ({ ...prev, ...newNames }));
      }
    };

    fetchNames();
  }, [location.pathname]); // Re-run when path changes

  const breadcrumbs = useMemo(() => {
    const pathParts = location.pathname.split('/').filter(Boolean);

    // Create breadcrumb items based on the URL
    const crumbs = [];

    // Always add the user type as the first level (home)
    let userTypeName = 'Venue';
    if (userType === 'artist') {
      userTypeName = 'Artist';
    } else if (userType === 'agency') {
      userTypeName = 'Agency';
    }

    crumbs.push({
      name: userTypeName,
      path: `/${userType}/dashboard`,
      isActive: pathParts.length === 2 && pathParts[1] === 'dashboard',
    });

    // Special case for documents pages
    if (pathParts[0] === 'documents' && pathParts.length > 1) {
      // Add documents section
      crumbs.push({
        name: 'Paperwork',
        path: `/${userType}/paperwork`,
        isActive: false,
      });

      // Add document name or fallback to ID
      const documentId = pathParts[1];
      const documentName = entityNames[`document-${documentId}`] || `Document ${documentId.substring(0, 8)}...`;
      crumbs.push({
        name: documentName,
        path: `/documents/${documentId}`,
        isActive: true,
      });

      return crumbs;
    }

    // Special case for bookings pages
    if (pathParts[0] === 'bookings' && pathParts.length > 1) {
      // Add bookings section
      crumbs.push({
        name: 'Bookings',
        path: `/${userType}/bookings`,
        isActive: false,
      });

      // Add booking name or fallback to ID
      const bookingId = pathParts[1];
      const bookingName = entityNames[`booking-${bookingId}`] || `Booking ${bookingId.substring(0, 8)}...`;
      crumbs.push({
        name: bookingName,
        path: `/bookings/${bookingId}`,
        isActive: true,
      });

      return crumbs;
    }

    // Special case for templates pages
    if (pathParts[0] === 'templates') {
      // Add templates section
      crumbs.push({
        name: 'Templates',
        path: '/templates',
        isActive: pathParts.length === 1,
      });

      // Add template ID or action for deeper paths
      if (pathParts.length > 1) {
        if (pathParts[1] === 'new') {
          crumbs.push({
            name: 'New Template',
            path: '/templates/new',
            isActive: true,
          });
        } else if (pathParts.length > 2 && pathParts[2] === 'edit') {
          // For edit pages like /templates/{id}/edit
          const templateId = pathParts[1];
          const templateName = entityNames[`template-${templateId}`] || `Template ${templateId.substring(0, 8)}...`;
          crumbs.push({
            name: templateName,
            path: `/templates/${templateId}`,
            isActive: false,
          });

          crumbs.push({
            name: 'Edit',
            path: `/templates/${templateId}/edit`,
            isActive: true,
          });
        } else {
          // For view pages like /templates/{id}
          const templateId = pathParts[1];
          const templateName = entityNames[`template-${templateId}`] || `Template ${templateId.substring(0, 8)}...`;
          crumbs.push({
            name: templateName,
            path: `/templates/${templateId}`,
            isActive: true,
          });
        }
      }

      return crumbs;
    }

    // Handle entity-specific routes (e.g., /agency/bookings/{id}, /venue/paperwork/{id})
    if (pathParts.length >= 3 && (pathParts[0] === userType)) {
      // Add the section
      crumbs.push({
        name: formatSectionName(pathParts[1]),
        path: `/${pathParts[0]}/${pathParts[1]}`,
        isActive: pathParts.length === 2,
      });

      // Handle specific entity pages with IDs
      if (pathParts.length > 2) {
        const section = pathParts[1];
        const id = pathParts[2];

        if (section === 'bookings') {
          const bookingName = entityNames[`booking-${id}`] || `Booking ${id.substring(0, 8)}...`;
          crumbs.push({
            name: bookingName,
            path: `/${pathParts[0]}/${pathParts[1]}/${id}`,
            isActive: pathParts.length === 3,
          });
        } else if (section === 'clients') {
          const clientName = entityNames[`client-${id}`] || `Client ${id.substring(0, 8)}...`;
          crumbs.push({
            name: clientName,
            path: `/${pathParts[0]}/${pathParts[1]}/${id}`,
            isActive: pathParts.length === 3,
          });
        } else if (section === 'paperwork') {
          const documentName = entityNames[`document-${id}`] || `Document ${id.substring(0, 8)}...`;
          crumbs.push({
            name: documentName,
            path: `/${pathParts[0]}/${pathParts[1]}/${id}`,
            isActive: pathParts.length === 3,
          });
        } else {
          // For other sections, use the regular formatting
          crumbs.push({
            name: formatSectionName(id),
            path: `/${pathParts.slice(0, 3).join('/')}`,
            isActive: pathParts.length === 3,
          });
        }

        // Handle additional levels (like edit pages)
        if (pathParts.length > 3) {
          for (let i = 3; i < pathParts.length; i++) {
            crumbs.push({
              name: formatSectionName(pathParts[i]),
              path: `/${pathParts.slice(0, i + 1).join('/')}`,
              isActive: i === pathParts.length - 1,
            });
          }
        }
      }
    } else {
      // Regular handling for other pages
      // Add the section if it exists
      if (pathParts.length >= 2 && pathParts[1] !== 'dashboard') {
        crumbs.push({
          name: formatSectionName(pathParts[1]),
          path: `/${pathParts[0]}/${pathParts[1]}`,
          isActive: pathParts.length === 2,
        });
      }

      // Add additional levels for deeper paths
      if (pathParts.length > 2) {
        for (let i = 2; i < pathParts.length; i++) {
          crumbs.push({
            name: formatSectionName(pathParts[i]),
            path: `/${pathParts.slice(0, i + 1).join('/')}`,
            isActive: i === pathParts.length - 1,
          });
        }
      }
    }

    return crumbs;
  }, [location.pathname, userType, entityNames]);

  // Helper to format section names
  function formatSectionName(section: string): string {
    // Convert kebab-case or snake_case to Title Case
    return section
      .replace(/[-_]/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // For mobile, we'll show only the first and last breadcrumb items
  const renderBreadcrumbs = () => {
    if (isMobile && breadcrumbs.length > 2) {
      // Show only home, ellipsis, and the current page
      const firstCrumb = breadcrumbs[0];
      const lastCrumb = breadcrumbs[breadcrumbs.length - 1];

      return (
        <>
          {/* Home icon */}
          <BreadcrumbItem>
            <BreadcrumbLink href={`/${userType}/dashboard`} className="flex items-center">
              <HomeIcon className="h-4 w-4 mr-1" />
            </BreadcrumbLink>
          </BreadcrumbItem>

          {/* First breadcrumb */}
          <BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbLink href={firstCrumb.path}>{firstCrumb.name}</BreadcrumbLink>
          </BreadcrumbItem>

          {/* Ellipsis for middle items */}
          <BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbEllipsis />
          </BreadcrumbItem>

          {/* Last breadcrumb (current page) */}
          <BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbPage>{lastCrumb.name}</BreadcrumbPage>
          </BreadcrumbItem>
        </>
      );
    }

    // Desktop view - show all breadcrumbs
    return (
      <>
        {/* Home icon */}
        <BreadcrumbItem>
          <BreadcrumbLink href={`/${userType}/dashboard`} className="flex items-center">
            <HomeIcon className="h-4 w-4 mr-1" />
          </BreadcrumbLink>
        </BreadcrumbItem>

        {/* All breadcrumbs */}
        {breadcrumbs.map((crumb) => (
          <BreadcrumbItem key={crumb.path}>
            <BreadcrumbSeparator />
            {crumb.isActive ? (
              <BreadcrumbPage>{crumb.name}</BreadcrumbPage>
            ) : (
              <BreadcrumbLink href={crumb.path}>{crumb.name}</BreadcrumbLink>
            )}
          </BreadcrumbItem>
        ))}
      </>
    );
  };

  return (
    <Breadcrumb className="bg-transparent w-full">
      <BreadcrumbList className="w-full">
        {renderBreadcrumbs()}
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default BreadcrumbNavigation;
