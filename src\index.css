
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;

    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 0%;

    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;

    --accent: 0 0% 96%;
    --accent-foreground: 0 0% 0%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89%;
    --input: 0 0% 89%;
    --ring: 0 0% 0%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 0 0% 10%;
    --sidebar-primary: 0 0% 0%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 96%;
    --sidebar-accent-foreground: 0 0% 10%;
    --sidebar-border: 0 0% 89%;
    --sidebar-ring: 0 0% 0%;
  }

  .dark {
    --background: 0 0% 7%;
    --foreground: 0 0% 98%;

    --card: 0 0% 7%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 7%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 0%;

    --secondary: 0 0% 14.5%;
    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 14.5%;
    --muted-foreground: 0 0% 65%;

    --accent: 0 0% 14.5%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 14.5%;
    --input: 0 0% 14.5%;
    --ring: 0 0% 83.1%;

    --sidebar-background: 0 0% 7%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 0 0% 98%;
    --sidebar-primary-foreground: 0 0% 0%;
    --sidebar-accent: 0 0% 14.5%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 0 0% 14.5%;
    --sidebar-ring: 0 0% 98%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer base {
  html {
    font-family: 'Inter', sans-serif;
  }
}

/* Required field styling */
.required::after {
  content: " *";
  color: #ef4444;
}

/* Contract template styles */
.contract-preview {
  max-width: none;
}

.contract-preview p,
.contract-preview div,
.contract-preview li,
.contract-preview td,
.contract-preview th,
.contract-preview blockquote,
.contract-preview pre {
  white-space: pre-wrap;
}

.contract-preview table {
  border-collapse: collapse;
}

.contract-preview td,
.contract-preview th {
  border: 1px solid #d1d5db;
  padding: 0.5rem;
}

.contract-preview th {
  background-color: #f3f4f6;
}
