
import { useState, useEffect } from 'react';
import { isAfter, isBefore, isEqual } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '@/components/layout/DashboardLayout';
import BookingCalendar from '@/components/bookings/BookingCalendar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Calendar as CalendarIcon, Search, RotateCcw, Loader2, MoreVertical, Eye, Clock, User, MapPin } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '@/components/ui/pagination';
import { supabase } from '@/core/api/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { DeleteConfirmationDialog } from '@/components/ui/delete-confirmation-dialog';
import { formatDateEU, formatTimeEU, formatCurrencyEU, calculateTotalPrice, useIsMobile } from '@/core';

interface VenueInfo {
  venue_name?: string;
  name?: string;
  id: string;
}

interface BookingEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  status: "confirmed" | "pending" | "cancelled" | "completed";
  venue: VenueInfo;
  fee?: number;
  hourlyRate?: number | null;
  pricing_type?: string;
}

interface CalendarBookingEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  status: "confirmed" | "pending" | "cancelled" | "completed";
  venue: string;
  address?: string;
}

// Artist bookings component
const ArtistBookings = () => {
  const navigate = useNavigate();
  const { profile } = useAuth();
  const isMobile = useIsMobile();
  const [bookings, setBookings] = useState<BookingEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: undefined,
    to: undefined
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [timeFilter, setTimeFilter] = useState('future');
  const [minPrice, setMinPrice] = useState('');
  const [maxPrice, setMaxPrice] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [bookingToDelete, setBookingToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'success';
      case 'pending':
        return 'warning';
      case 'cancelled':
        return 'destructive';
      case 'completed':
        return 'default';
      default:
        return 'outline';
    }
  };

  useEffect(() => {
    if (profile?.id) {
      fetchBookings();
    }
  }, [profile]);

  const fetchBookings = async () => {
    if (!profile?.id) return;

    try {
      setIsLoading(true);

      // Use entity ID if available, otherwise fall back to user ID
      const artistId = profile.entity?.id || profile.id;
      ('Fetching bookings for artist ID:', artistId);

      // Try to fetch bookings using owner_entity_id first
      const { data: ownerData, error: ownerError } = await supabase
        .from('bookings')
        .select(`
          id,
          title,
          booking_start,
          booking_end,
          status,
          venue_id,
          price,
          pricing_type
        `)
        .eq('owner_entity_id', artistId);

      if (ownerError) {
        console.error('Error fetching bookings with owner_entity_id:', ownerError);
      }

      // If we found bookings with owner_entity_id, use those
      if (ownerData && ownerData.length > 0) {
        ('Found bookings with owner_entity_id:', ownerData.length);
        const data = ownerData;
        return { data, error: null };
      }

      // Otherwise, fall back to artist_id
      ('No bookings found with owner_entity_id, trying artist_id');
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          id,
          title,
          booking_start,
          booking_end,
          status,
          venue_id,
          price,
          pricing_type
        `)
        .eq('artist_id', artistId);

      if (error) {
        toast.error("Error loading bookings");
        throw error;
      }

      // Now for each booking, get the venue details
      const formattedBookings = await Promise.all(data.map(async (booking) => {
        // Get the venue entity name
        const { data: entityData, error: entityError } = await supabase
          .from('entities')
          .select('name')
          .eq('id', booking.venue_id)
          .single();

        if (entityError) {
          console.error('Error fetching venue entity:', entityError);
          // Continue with partial data even if venue name is not found
        }

        // Calculate total price for hourly bookings
        let fee = parseFloat(String(booking.price || "0"));
        if (booking.pricing_type === 'hourly') {
          fee = calculateTotalPrice(
            booking.price,
            'hourly',
            booking.booking_start,
            booking.booking_end
          );
        }

        return {
          id: booking.id,
          title: booking.title,
          start: new Date(booking.booking_start),
          end: new Date(booking.booking_end),
          status: booking.status as "confirmed" | "pending" | "cancelled" | "completed",
          venue: {
            venue_name: entityData?.name || 'Unknown Venue',
            name: entityData?.name || 'Unknown Venue',
            id: booking.venue_id
          },
          fee: fee,
          hourlyRate: booking.pricing_type === 'hourly' ? parseFloat(String(booking.price || "0")) : null,
          pricing_type: booking.pricing_type
        };
      }));

      setBookings(formattedBookings);
    } catch (error) {
      console.error('Error fetching bookings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Delete functionality removed as requested

  const confirmDeleteBooking = async () => {
    if (!bookingToDelete) return;

    try {
      setIsDeleting(true);

      // First check if there are any documents associated with this booking
      const { data: documents, error: docError } = await supabase
        .from("documents")
        .select("id, file_url")
        .eq("booking_id", bookingToDelete);

      if (docError) {
        console.error("Error checking for documents:", docError);
        throw new Error("Failed to check for associated documents");
      }

      // If there are documents, delete them first
      if (documents && documents.length > 0) {
        (`Deleting ${documents.length} documents associated with booking ${bookingToDelete}`);

        // Delete each document
        for (const doc of documents) {
          const { error: deleteDocError } = await supabase
            .from("documents")
            .delete()
            .eq("id", doc.id);

          if (deleteDocError) {
            console.error(`Error deleting document ${doc.id}:`, deleteDocError);
            // Continue with other documents even if one fails
          }

          // Try to delete the file from storage if it exists
          if (doc.file_url) {
            try {
              const filePath = doc.file_url.split('/').pop();
              if (filePath) {
                await supabase.storage
                  .from('documents')
                  .remove([`${bookingToDelete}/${filePath}`]);
              }
            } catch (storageError) {
              console.error("Error deleting file from storage:", storageError);
              // Continue even if storage deletion fails
            }
          }
        }
      }

      // Now delete the booking
      const { error } = await supabase
        .from("bookings")
        .delete()
        .eq("id", bookingToDelete);

      if (error) {
        console.error("Error deleting booking:", error);
        throw error;
      }

      toast.success("Booking deleted successfully");
      fetchBookings();
    } catch (error: any) {
      console.error("Error deleting booking:", error);
      toast.error("Failed to delete booking: " + (error.message || "Unknown error"));
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
      setBookingToDelete(null);
    }
  };

  // Sort bookings based on time filter
  const sortedBookings = [...bookings].sort((a, b) => {
    if (timeFilter === 'past') {
      // For past bookings: most recent first
      return new Date(b.start).getTime() - new Date(a.start).getTime();
    } else {
      // For future bookings: earliest first
      return new Date(a.start).getTime() - new Date(b.start).getTime();
    }
  });

  const filteredEvents = sortedBookings.filter(event => {
    if (timeFilter === 'future' && isBefore(event.start, new Date())) return false;
    if (timeFilter === 'past' && (isAfter(event.start, new Date()) || isEqual(event.start, new Date()))) return false;
    if (dateRange?.from && isBefore(event.start, dateRange.from)) return false;
    if (dateRange?.to && isAfter(event.start, dateRange.to)) return false;

    const minPriceNum = minPrice ? parseFloat(minPrice) : null;
    const maxPriceNum = maxPrice ? parseFloat(maxPrice) : null;

    if (minPriceNum !== null && event.fee && event.fee < minPriceNum) return false;
    if (maxPriceNum !== null && event.fee && event.fee > maxPriceNum) return false;

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return event.title.toLowerCase().includes(query) ||
             (event.venue?.venue_name && event.venue.venue_name.toLowerCase().includes(query)) ||
             (event.venue?.name && event.venue.name.toLowerCase().includes(query));
    }
    return true;
  });

  const indexOfLastEvent = currentPage * rowsPerPage;
  const indexOfFirstEvent = indexOfLastEvent - rowsPerPage;
  const currentEvents = filteredEvents.slice(indexOfFirstEvent, indexOfLastEvent);
  const pageCount = Math.ceil(filteredEvents.length / rowsPerPage);

  const handlePageChange = (page: number) => {
    if (page > 0 && page <= pageCount) {
      setCurrentPage(page);
    }
  };

  const resetFilters = () => {
    setDateRange({
      from: undefined,
      to: undefined
    });
    setSearchQuery('');
    setTimeFilter('future');
    setMinPrice('');
    setMaxPrice('');
    setCurrentPage(1);
  };

  // Need to fetch the location data for each booking
  const fetchBookingLocations = async () => {
    const bookingIds = bookings.map(booking => booking.id);
    if (bookingIds.length === 0) return {};

    try {
      const { data, error } = await supabase
        .from('bookings')
        .select('id, location')
        .in('id', bookingIds);

      if (error) {
        console.error('Error fetching booking locations:', error);
        return {};
      }

      // Create a map of booking ID to location
      const locationMap: Record<string, string> = {};
      data.forEach(booking => {
        locationMap[booking.id] = booking.location || '';
      });

      return locationMap;
    } catch (err) {
      console.error('Exception in fetchBookingLocations:', err);
      return {};
    }
  };

  // Use useEffect to fetch locations when bookings change
  const [bookingLocations, setBookingLocations] = useState<Record<string, string>>({});

  useEffect(() => {
    if (bookings.length > 0) {
      fetchBookingLocations().then(locations => {
        setBookingLocations(locations);
      });
    }
  }, [bookings]);

  const calendarEvents: CalendarBookingEvent[] = filteredEvents.map(booking => {
    // Normalize status to ensure consistency (handle both 'canceled' and 'cancelled' spellings)
    let normalizedStatus = booking.status;
    if (normalizedStatus === 'canceled') {
      normalizedStatus = 'cancelled';
    }

    return {
      id: booking.id,
      title: booking.title,
      start: booking.start,
      end: booking.end,
      status: normalizedStatus,
      venue: booking.venue?.venue_name || booking.venue?.name || 'Unknown Venue',
      address: bookingLocations[booking.id] || 'No address provided' // Use the actual booking location
    };
  });

  const navigateToBookingDetails = (id: string) => {
    navigate(`/artist/bookings/${id}`);
  };

  return (
    <DashboardLayout userType="artist">
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Bookings</h1>
        </div>

        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
          <Tabs defaultValue="list" className="w-full">
            <TabsList className='bg-zinc-200'>
              <TabsTrigger value="list">List View</TabsTrigger>
              <TabsTrigger value="calendar">Calendar View</TabsTrigger>
            </TabsList>

            <TabsContent value="list" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>All Bookings</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="mb-6">
                    {isMobile ? (
                      <div className="space-y-3">
                        {/* Search bar - full width on mobile */}
                        <div className="relative w-full">
                          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input
                            placeholder="Search venues or event titles..."
                            value={searchQuery}
                            onChange={e => setSearchQuery(e.target.value)}
                            className="pl-8 w-full"
                          />
                        </div>

                        {/* Filter controls in a grid */}
                        <div className="grid grid-cols-2 gap-2">
                          <Select value={timeFilter} onValueChange={setTimeFilter}>
                            <SelectTrigger className="w-full">
                              <SelectValue placeholder="Time period" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All bookings</SelectItem>
                              <SelectItem value="future">Future bookings</SelectItem>
                              <SelectItem value="past">Past bookings</SelectItem>
                            </SelectContent>
                          </Select>

                          <Popover>
                            <PopoverTrigger asChild>
                              <Button variant="outline" className="w-full justify-start text-left font-normal text-xs h-10 truncate">
                                <CalendarIcon className="mr-1 h-3.5 w-3.5 flex-shrink-0" />
                                <span className="truncate">
                                  {dateRange?.from ? dateRange.to ?
                                    `${formatDateEU(dateRange.from)} - ${formatDateEU(dateRange.to)}` :
                                    formatDateEU(dateRange.from) : "Date range"}
                                </span>
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                initialFocus
                                mode="range"
                                defaultMonth={dateRange?.from}
                                selected={dateRange}
                                onSelect={setDateRange}
                                numberOfMonths={1}
                              />
                            </PopoverContent>
                          </Popover>

                          {/* Price range inputs in a single row */}
                          <div className="col-span-2 grid grid-cols-2 gap-2">
                            <div className="relative">
                              <Input
                                type="number"
                                placeholder="Min €"
                                value={minPrice}
                                onChange={e => setMinPrice(e.target.value)}
                                className="w-full pl-6"
                              />
                              <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-xs text-gray-500">€</span>
                            </div>
                            <div className="relative">
                              <Input
                                type="number"
                                placeholder="Max €"
                                value={maxPrice}
                                onChange={e => setMaxPrice(e.target.value)}
                                className="w-full pl-6"
                              />
                              <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-xs text-gray-500">€</span>
                            </div>
                          </div>

                          {/* Reset button - full width */}
                          <Button
                            variant="outline"
                            onClick={resetFilters}
                            className="col-span-2 h-9 text-xs"
                            title="Reset Filters"
                          >
                            <RotateCcw className="h-3.5 w-3.5 mr-1.5" />
                            Reset Filters
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div className="flex flex-wrap gap-3 items-center">
                        <div className="relative flex-1 min-w-[200px]">
                          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                          <Input placeholder="Search venues or event titles..." value={searchQuery} onChange={e => setSearchQuery(e.target.value)} className="pl-8" />
                        </div>

                        <Select value={timeFilter} onValueChange={setTimeFilter}>
                          <SelectTrigger className="w-[140px]">
                            <SelectValue placeholder="Time period" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All bookings</SelectItem>
                            <SelectItem value="future">Future bookings</SelectItem>
                            <SelectItem value="past">Past bookings</SelectItem>
                          </SelectContent>
                        </Select>

                        <Popover>
                          <PopoverTrigger asChild>
                            <Button variant="outline" className="w-auto justify-start text-left font-normal">
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {dateRange?.from ? dateRange.to ? <>
                                    {formatDateEU(dateRange.from)} - {formatDateEU(dateRange.to)}
                                  </> : formatDateEU(dateRange.from) : "Date range"}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar initialFocus mode="range" defaultMonth={dateRange?.from} selected={dateRange} onSelect={setDateRange} numberOfMonths={2} />
                          </PopoverContent>
                        </Popover>

                        <div className="flex items-center gap-2">
                          <Input type="number" placeholder="Min €" value={minPrice} onChange={e => setMinPrice(e.target.value)} className="w-24" />
                          <span>-</span>
                          <Input type="number" placeholder="Max €" value={maxPrice} onChange={e => setMaxPrice(e.target.value)} className="w-24" />
                        </div>

                        <Button variant="outline" onClick={resetFilters} size="icon" title="Reset Filters">
                          <RotateCcw className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>

                  {isLoading ? (
                    <div className="flex justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
                    </div>
                  ) : filteredEvents.length > 0 ? (
                    <>
                      {isMobile ? (
                        <div className="flex flex-col space-y-3 p-0">
                          {currentEvents.map(booking => (
                            <div
                              key={booking.id}
                              className="border rounded-lg p-3 cursor-pointer hover:bg-muted/50 transition-colors shadow-sm"
                              onClick={() => navigateToBookingDetails(booking.id)}
                            >
                              {/* Date badge with calendar icon and status badge */}
                              <div className="flex items-center mb-2">
                                <div className="bg-gray-100 rounded-md p-1.5 mr-2 flex items-center">
                                  <Calendar className="h-3.5 w-3.5 text-gray-700" />
                                  <span className="ml-1 text-xs font-medium">{formatDateEU(booking.start)}</span>
                                </div>
                                <Badge
                                  variant={getStatusBadgeVariant(booking.status)}
                                  className="ml-auto flex-shrink-0"
                                >
                                  {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                                </Badge>
                              </div>

                              {/* Event title */}
                              <h4 className="font-medium text-sm mb-2 line-clamp-1">{booking.title}</h4>

                              {/* Event details in a clean layout */}
                              <div className="space-y-1.5 text-xs">
                                {/* Time info */}
                                <div className="flex items-center text-gray-600">
                                  <Clock className="h-3 w-3 mr-1.5 text-gray-500" />
                                  <span>{formatTimeEU(booking.start)} - {formatTimeEU(booking.end)}</span>
                                </div>

                                {/* Venue info */}
                                <div className="flex items-center text-gray-600">
                                  <MapPin className="h-3 w-3 mr-1.5 text-gray-500" />
                                  <span className="truncate">{booking.venue?.venue_name || booking.venue?.name || 'Unknown'}</span>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="rounded-md border">
                          <Table>
                            <TableHeader>
                              <TableRow>
                                <TableHead>Event</TableHead>
                                <TableHead>Venue</TableHead>
                                <TableHead>Date & Time</TableHead>
                                <TableHead>Fee</TableHead>
                                <TableHead>Status</TableHead>
                                <TableHead className="text-right">Actions</TableHead>
                              </TableRow>
                            </TableHeader>
                            <TableBody>
                              {currentEvents.map(booking => (
                                <TableRow key={booking.id} className="cursor-pointer" onClick={() => navigateToBookingDetails(booking.id)}>
                                  <TableCell>
                                    <div className="font-medium">{booking.title}</div>
                                  </TableCell>
                                  <TableCell>
                                    {booking.venue?.venue_name || booking.venue?.name || 'Unknown Venue'}
                                  </TableCell>
                                  <TableCell>
                                    <div>{formatDateEU(booking.start)}</div>
                                    <div className="text-sm text-gray-500">{formatTimeEU(booking.start)} - {formatTimeEU(booking.end)}</div>
                                  </TableCell>
                                  <TableCell>
                                    {booking.fee !== undefined && (
                                      <div>
                                        {booking.pricing_type === 'hourly' ? (
                                          <>
                                            <div>{formatCurrencyEU(booking.fee)}</div>
                                            <div className="text-xs text-gray-500">
                                              ({formatCurrencyEU(booking.hourlyRate || 0)}/hour)
                                            </div>
                                          </>
                                        ) : (
                                          formatCurrencyEU(booking.fee)
                                        )}
                                      </div>
                                    )}
                                  </TableCell>
                                  <TableCell>
                                    <div className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold
                                      ${booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                                        booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                        booking.status === 'completed' ? 'bg-blue-100 text-blue-800' :
                                        'bg-red-100 text-red-800'}`}>
                                      {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                                    </div>
                                  </TableCell>
                                  <TableCell className="text-right">
                                    <DropdownMenu>
                                      <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                          <span className="sr-only">Open menu</span>
                                          <MoreVertical className="h-4 w-4" />
                                        </Button>
                                      </DropdownMenuTrigger>
                                      <DropdownMenuContent align="end">
                                        <DropdownMenuItem onClick={(e) => {
                                          e.stopPropagation();
                                          navigate(`/artist/bookings/${booking.id}`);
                                        }}>
                                          <Eye className="mr-2 h-4 w-4" />
                                          <span>View Details</span>
                                        </DropdownMenuItem>
                                      </DropdownMenuContent>
                                    </DropdownMenu>
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </div>
                      )}

                      {pageCount > 1 && (
                        <div className="mt-4 flex justify-center">
                          <Pagination>
                            <PaginationContent>
                              <PaginationItem>
                                <PaginationPrevious
                                  onClick={() => handlePageChange(currentPage - 1)}
                                  className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                                />
                              </PaginationItem>

                              {Array.from({ length: pageCount }, (_, i) => i + 1).map(page => (
                                <PaginationItem key={page}>
                                  <PaginationLink
                                    isActive={currentPage === page}
                                    onClick={() => handlePageChange(page)}
                                  >
                                    {page}
                                  </PaginationLink>
                                </PaginationItem>
                              ))}

                              <PaginationItem>
                                <PaginationNext
                                  onClick={() => handlePageChange(currentPage + 1)}
                                  className={currentPage === pageCount ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                                />
                              </PaginationItem>
                            </PaginationContent>
                          </Pagination>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="text-center py-8 bg-gray-50 rounded-lg">
                      <p className="text-gray-500">No bookings found matching your filters</p>
                      {(searchQuery || timeFilter !== 'all' || dateRange?.from || minPrice || maxPrice) && (
                        <Button variant="outline" onClick={resetFilters} className="mt-4">
                          <RotateCcw className="mr-2 h-4 w-4" />
                          Reset Filters
                        </Button>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="calendar" className="mt-6">
              <BookingCalendar events={calendarEvents} userType="artist" />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      <DeleteConfirmationDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        onConfirm={confirmDeleteBooking}
        title="Delete Booking"
        description="Are you sure you want to delete this booking? This will also delete all associated documents. This action cannot be undone."
        itemType="booking"
        isDeleting={isDeleting}
      />
    </DashboardLayout>
  );
};

export default ArtistBookings;
