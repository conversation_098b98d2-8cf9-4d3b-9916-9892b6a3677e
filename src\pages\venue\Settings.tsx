import React from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/contexts/AuthContext';
import { FileText } from 'lucide-react';
import { Link } from 'react-router-dom';

const VenueSettings = () => {
  const { user } = useAuth();

  return (
    <DashboardLayout userType="venue">
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Venue Settings</h1>

        <Card>
          <CardHeader>
            <CardTitle>Document management</CardTitle>
            <CardDescription>
              Manage your document templates and settings
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-3 border rounded-md">
              <div className="flex items-center space-x-3">
                <FileText className="h-5 w-5 text-gray-500" />
                <div>
                  <p className="font-medium">Document Templates</p>
                  <p className="text-xs text-gray-500">
                    Create and manage templates for your venue documents
                  </p>
                </div>
              </div>
              <Button asChild>
                <Link to="/templates" className="flex items-center">
                  <FileText className="h-4 w-4 mr-2" />
                  Manage Templates
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>


      </div>
    </DashboardLayout>
  );
};

export default VenueSettings;
