import { supabase } from '@/core/api/supabase';

/**
 * Generate a secure random token for user invitations
 * @returns A secure random token string
 */
export const generateInvitationToken = async (): Promise<string> => {
  // Generate a random string using the Web Crypto API
  const array = new Uint8Array(32);
  window.crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
};

/**
 * Get invitation details by token
 * @param token The invitation token
 * @returns The invitation details or null if not found
 */
export const getInvitationByToken = async (token: string) => {
  try {
    const { data, error } = await supabase
      .from('entity_invitations')
      .select(`
        id,
        entity_id,
        email,
        role,
        status,
        expires_at,
        entities (
          id,
          name,
          entity_type
        )
      `)
      .eq('token', token)
      .eq('status', 'pending')
      .single();

    if (error) {
      console.error('Error fetching invitation:', error);
      return null;
    }

    // Check if invitation has expired
    if (data && new Date(data.expires_at) < new Date()) {
      // Update status to expired
      await supabase
        .from('entity_invitations')
        .update({ status: 'expired' })
        .eq('id', data.id);
      
      return { ...data, status: 'expired' };
    }

    return data;
  } catch (error) {
    console.error('Exception in getInvitationByToken:', error);
    return null;
  }
};

/**
 * Accept an invitation and create entity_user relationship
 * @param invitationId The invitation ID
 * @param userId The user ID accepting the invitation
 * @returns Success status
 */
export const acceptInvitation = async (invitationId: string, userId: string) => {
  try {
    // Get the invitation details
    const { data: invitation, error: invitationError } = await supabase
      .from('entity_invitations')
      .select('entity_id, role, status')
      .eq('id', invitationId)
      .single();

    if (invitationError || !invitation) {
      console.error('Error fetching invitation:', invitationError);
      return { success: false, error: 'Invitation not found' };
    }

    if (invitation.status !== 'pending') {
      return { success: false, error: `Invitation is ${invitation.status}` };
    }

    // Start a transaction
    const { error: transactionError } = await supabase.rpc('accept_invitation', {
      p_invitation_id: invitationId,
      p_user_id: userId,
      p_entity_id: invitation.entity_id,
      p_role: invitation.role
    });

    if (transactionError) {
      console.error('Error accepting invitation:', transactionError);
      return { success: false, error: transactionError.message };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Exception in acceptInvitation:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Get all invitations for an entity
 * @param entityId The entity ID
 * @returns Array of invitations
 */
export const getEntityInvitations = async (entityId: string) => {
  try {
    const { data, error } = await supabase
      .from('entity_invitations')
      .select(`
        id,
        email,
        role,
        status,
        expires_at,
        created_at,
        invited_by,
        users (
          name,
          email
        )
      `)
      .eq('entity_id', entityId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching entity invitations:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Exception in getEntityInvitations:', error);
    return [];
  }
};

/**
 * Resend an invitation email
 * @param invitationId The invitation ID
 * @returns Success status
 */
export const resendInvitation = async (invitationId: string) => {
  try {
    // Get the invitation details
    const { data: invitation, error: invitationError } = await supabase
      .from('entity_invitations')
      .select(`
        id,
        entity_id,
        email,
        role,
        token,
        entities (
          name,
          entity_type
        )
      `)
      .eq('id', invitationId)
      .single();

    if (invitationError || !invitation) {
      console.error('Error fetching invitation:', invitationError);
      return { success: false, error: 'Invitation not found' };
    }

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return { success: false, error: 'Authentication error' };
    }

    // Calculate new expiration date (7 days from now)
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7);

    // Update the expiration date
    const { error: updateError } = await supabase
      .from('entity_invitations')
      .update({
        expires_at: expiresAt.toISOString(),
        status: 'pending'
      })
      .eq('id', invitation.id);

    if (updateError) {
      console.error('Error updating invitation:', updateError);
      return { success: false, error: updateError.message };
    }

    // Note: Email functionality has been removed
    // Invitation links are now shared manually by copying the URL

    return { success: true };
  } catch (error: any) {
    console.error('Exception in resendInvitation:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Cancel an invitation
 * @param invitationId The invitation ID
 * @returns Success status
 */
export const cancelInvitation = async (invitationId: string) => {
  try {
    const { error } = await supabase
      .from('entity_invitations')
      .delete()
      .eq('id', invitationId);

    if (error) {
      console.error('Error canceling invitation:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error: any) {
    console.error('Exception in cancelInvitation:', error);
    return { success: false, error: error.message };
  }
};
