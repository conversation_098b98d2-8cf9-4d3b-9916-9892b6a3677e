import { Session, User } from '@supabase/supabase-js';

export interface EntityData {
  id: string;
  type: 'artist' | 'venue' | 'agency';
  name: string;
  description?: string | null;
  is_primary?: boolean;
}

export interface ArtistData {
  artist_name: string | null;
  profile_image_url?: string | null;
  genre?: string[] | null;
  region?: string | null;
}

export interface VenueData {
  venue_name: string | null;
  venue_type?: string | null;
  address?: string | null;
  logo_url?: string | null;
}

export interface AgencyData {
  agency_name: string | null;
  profile_image_url?: string | null;
  banner_image_url?: string | null;
  company_name?: string | null;
  vat_number?: string | null;
  invoice_address?: string | null;
}

export interface UserProfile {
  id: string;
  email: string;
  name: string;
  user_type: 'artist' | 'venue' | 'agency';
  phone_number?: string | null;
  profile_image_url?: string | null;
  entity?: EntityData;
  artist_data?: ArtistData;
  venue_data?: VenueData;
  agency_data?: AgencyData;
}

export interface AuthContextType {
  session: Session | null;
  user: User | null;
  profile: UserProfile | null;
  loading: boolean;
  signOut: () => Promise<void>;
  isArtist: boolean;
  isVenue: boolean;
  isAgency: boolean;
}
