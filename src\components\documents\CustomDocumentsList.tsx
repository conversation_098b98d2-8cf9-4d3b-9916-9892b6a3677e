import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/core/api/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Paperclip, Eye, Trash2, Loader2, AlertCircle, MoreVertical } from 'lucide-react';
import { format } from 'date-fns';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';

interface Document {
  id: string;
  name: string;
  document_type: string;
  created_at: string;
  status: string;
  file_url: string;
  booking_id?: string;
  title?: string;
}

interface CustomDocumentsListProps {
  userType: 'artist' | 'venue' | 'agency';
  documentType: 'all' | 'contract' | 'rider' | 'callsheet' | 'other';
  bookingId?: string;
}

const CustomDocumentsList = ({ userType, documentType, bookingId }: CustomDocumentsListProps) => {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { profile } = useAuth();

  useEffect(() => {
    fetchDocuments();
  }, [userType, documentType, bookingId]);

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!profile?.entity?.id) {
        setError('User profile or entity information not found');
        return;
      }

      let query = supabase
        .from('documents')
        .select('*');

      // Filter by document type if not 'all'
      if (documentType !== 'all') {
        query = query.eq('document_type', documentType);
      }

      // Filter by booking ID if provided
      if (bookingId) {
        query = query.eq('booking_id', bookingId);
      } else {
        // If no booking ID, filter by user's entity
        if (userType === 'artist') {
          query = query.eq('artist_id', profile.entity.id);
        } else if (userType === 'venue') {
          query = query.eq('venue_id', profile.entity.id);
        } else if (userType === 'agency') {
          // For agencies, we need to get all documents for their artists
          query = query.eq('created_by', profile.id);
        }
      }

      // Order by creation date, newest first
      query = query.order('created_at', { ascending: false });

      const { data, error: fetchError } = await query;

      if (fetchError) {
        console.error('Error fetching documents:', fetchError);
        setError('Failed to load documents. Please try again.');
        return;
      }

      setDocuments(data as Document[]);
    } catch (err) {
      console.error('Exception in fetchDocuments:', err);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleViewDocument = (documentId: string) => {
    navigate(`/documents/${documentId}`);
  };

  const handleDeleteDocument = async (documentId: string) => {
    try {
      const { error } = await supabase
        .from('documents')
        .delete()
        .eq('id', documentId);

      if (error) {
        console.error('Error deleting document:', error);
        toast.error('Failed to delete document');
        return;
      }

      // Remove the document from the local state
      setDocuments(documents.filter(doc => doc.id !== documentId));
      toast.success('Document deleted successfully');
    } catch (err) {
      console.error('Exception in handleDeleteDocument:', err);
      toast.error('An unexpected error occurred');
    }
  };

  const getDocumentTypeLabel = (type: string) => {
    switch (type) {
      case 'contract':
        return 'Contract';
      case 'rider':
        return 'Rider';
      case 'callsheet':
        return 'Call Sheet';
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'signed':
        return 'success';
      case 'uploaded':
        return 'default';
      case 'generated':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  if (error) {
    return (
      <Alert variant="destructive" className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-6 w-6 animate-spin text-gray-400" />
      </div>
    );
  }

  if (documents.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">No documents found.</p>
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[50px]">#</TableHead>
          <TableHead>Name</TableHead>
          <TableHead>Type</TableHead>
          <TableHead>Created</TableHead>
          <TableHead>Status</TableHead>
          <TableHead className="w-[70px]"></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {documents.map((doc, index) => (
          <TableRow
            key={doc.id}
            className="cursor-pointer hover:bg-muted/50 transition-colors"
            onClick={() => handleViewDocument(doc.id)}
          >
            <TableCell className="font-medium">{index + 1}</TableCell>
            <TableCell>
              {doc.title || 'Untitled Document'}
            </TableCell>
            <TableCell>
              <Badge variant="outline">
                {getDocumentTypeLabel(doc.document_type)}
              </Badge>
            </TableCell>
            <TableCell>
              {format(new Date(doc.created_at), 'MMM d, yyyy')}
            </TableCell>
            <TableCell>
              <Badge variant={getStatusBadgeVariant(doc.status)}>
                {doc.status.charAt(0).toUpperCase() + doc.status.slice(1)}
              </Badge>
            </TableCell>
            <TableCell className="text-right">
              <DropdownMenu>
                <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                  <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
                    <span className="sr-only">Open menu</span>
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={(e) => {
                    e.stopPropagation();
                    handleViewDocument(doc.id);
                  }}>
                    <Eye className="h-4 w-4 mr-2" />
                    View
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteDocument(doc.id);
                    }}
                    className="text-red-600 focus:text-red-600"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
};

export default CustomDocumentsList;
