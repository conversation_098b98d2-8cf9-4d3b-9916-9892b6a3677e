
import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/core';
import {
  Calendar,
  FileText,
  Settings,
  Home,
  FileEdit,
  X,
  Building2
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';

type SidebarProps = {
  userType: 'venue' | 'artist' | 'agency';
  isMobile?: boolean;
  isOpen?: boolean;
  onClose?: () => void;
};

const SIDEBAR_STATE_KEY = 'stagecloud-sidebar-state';

const Sidebar = ({ userType, isMobile = false, isOpen = false, onClose }: SidebarProps) => {
  const location = useLocation();
  const [collapsed, setCollapsed] = useState(() => {
    // Get initial state from localStorage or default to true (collapsed)
    const savedState = localStorage.getItem(SIDEBAR_STATE_KEY);
    return savedState ? savedState === 'collapsed' : true;
  });

  const { } = useAuth(); // Auth context available if needed

  const venueNavItems = [
    { name: 'Dashboard', href: '/venue/dashboard', icon: Home },
    { name: 'Bookings', href: '/venue/bookings', icon: Calendar },
    { name: 'Paperwork', href: '/venue/paperwork', icon: FileText },
    { name: 'Templates', href: '/templates', icon: FileEdit },
  ];

  const artistNavItems = [
    { name: 'Dashboard', href: '/artist/dashboard', icon: Home },
    { name: 'Bookings', href: '/artist/bookings', icon: Calendar },
    { name: 'Paperwork', href: '/artist/paperwork', icon: FileText },
  ];

  const agencyNavItems = [
    { name: 'Dashboard', href: '/agency/dashboard', icon: Home },
    { name: 'Bookings', href: '/agency/bookings', icon: Calendar },
    { name: 'Clients', href: '/agency/clients', icon: Building2 },
    { name: 'Paperwork', href: '/agency/paperwork', icon: FileText },
    { name: 'Templates', href: '/templates', icon: FileEdit },
  ];

  // Define the type for navigation items
  interface NavItem {
    name: string;
    href: string;
    icon: React.ComponentType<any>;
  }

  let navItems: NavItem[];
  if (userType === 'venue') {
    navItems = venueNavItems;
  } else if (userType === 'artist') {
    navItems = artistNavItems;
  } else {
    navItems = agencyNavItems;
  }

  // Toggle sidebar function for logo click
  const toggleSidebar = () => {
    const newState = !collapsed;
    setCollapsed(newState);
    // Save to localStorage
    localStorage.setItem(SIDEBAR_STATE_KEY, newState ? 'collapsed' : 'expanded');
  };

  // If on mobile and sidebar is not open, don't render it
  if (isMobile && !isOpen) {
    return null;
  }

  return (
    <>
      {/* Mobile overlay */}
      {isMobile && isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40"
          onClick={onClose}
          aria-hidden="true"
        />
      )}
      <aside
        className={cn(
          "bg-white border-r border-gray-200 h-screen transition-all duration-300 flex flex-col overflow-hidden",
          // Mobile specific styles
          isMobile
            ? "fixed left-0 top-0 z-50 w-64"
            : "sticky top-0",
          // Desktop collapsed state
          !isMobile && (collapsed ? "w-16" : "w-64")
        )}
      >
      {/* Sidebar header with clickable logo */}
      <div
        className="p-3 border-b border-gray-200 flex items-center justify-between h-14 cursor-pointer"
        onClick={isMobile ? undefined : toggleSidebar}
      >
        {/* Mobile close button */}
        {isMobile && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-2 top-2 rounded-md h-8 w-8 flex items-center justify-center text-gray-500 hover:bg-gray-100 hover:text-gray-900"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
        {(isMobile || !collapsed) && (
          <div className="flex items-center">
            <div className="h-7 w-7 bg-stagecloud-black rounded-md flex items-center justify-center">
              <span className="text-white font-bold text-xs">S.</span>
            </div>
            <span className="ml-2 text-lg font-bold text-gray-900">StageCloud</span>
          </div>
        )}
        {!isMobile && collapsed && (
          <div className="h-7 w-7 bg-stagecloud-black rounded-md flex items-center justify-center mx-auto">
            <span className="text-white font-bold text-xs">S.</span>
          </div>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-3 space-y-1 overflow-y-auto w-full">
        {navItems.map((item) => {
          const isActive = location.pathname === item.href;

          return (
            <Link
              key={item.name}
              to={item.href}
              className={cn(
                "group flex items-center rounded-md transition-colors aspect-square",
                isActive
                  ? "bg-stagecloud-black text-white"
                  : "text-gray-700 hover:bg-gray-100",
                isMobile
                  ? "h-10 w-full p-2"
                  : collapsed
                    ? "w-10 h-10 justify-center mx-auto"
                    : "h-10 w-full p-2"
              )}
            >
              <item.icon
                className={cn(
                  "flex-shrink-0",
                  isActive
                    ? "text-white"
                    : "text-gray-500 group-hover:text-gray-900",
                  "h-5 w-5"
                )}
              />
              {(isMobile || !collapsed) && (
                <span className="ml-3 text-sm font-medium truncate">{item.name}</span>
              )}
            </Link>
          );
        })}
      </nav>

      {/* Settings at bottom */}
      <div className="p-3 border-t border-gray-200 w-full">
        {/* Settings link */}
        <Link
          to={`/${userType}/settings`}
          className={cn(
            "group flex items-center rounded-md transition-colors aspect-square",
            location.pathname === `/${userType}/settings`
              ? "bg-stagecloud-black text-white"
              : "text-gray-700 hover:bg-gray-100",
            isMobile
              ? "h-10 w-full p-2"
              : collapsed
                ? "w-10 h-10 justify-center mx-auto"
                : "h-10 w-full p-2"
          )}
        >
          <Settings
            className={cn(
              "flex-shrink-0",
              location.pathname === `/${userType}/settings`
                ? "text-white"
                : "text-gray-500 group-hover:text-gray-900",
              "h-5 w-5"
            )}
          />
          {(isMobile || !collapsed) && (
            <span className="ml-3 text-sm font-medium truncate">Settings</span>
          )}
        </Link>

        {/* Language switch dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button
              className={cn(
                "group flex items-center rounded-md transition-colors aspect-square mt-3",
                "text-gray-700 hover:bg-gray-100",
                isMobile
                  ? "h-10 w-full p-2"
                  : collapsed
                    ? "w-10 h-10 justify-center mx-auto"
                    : "h-10 w-full p-2"
              )}
            >
              <div className="flex items-center flex-shrink-0">
                <div className="w-5 h-5 rounded overflow-hidden flex-shrink-0 border border-gray-200">
                  <div className="bg-blue-500 h-[40%]"></div>
                  <div className="bg-white h-[20%]"></div>
                  <div className="bg-red-500 h-[40%]"></div>
                </div>
                {(isMobile || !collapsed) && (
                  <span className="ml-3 text-sm font-medium truncate">English</span>
                )}
              </div>
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-40 bg-white shadow-md border border-gray-200">
            <DropdownMenuItem className="cursor-pointer flex items-center gap-2 py-2">
              <div className="w-5 h-5 rounded overflow-hidden border border-gray-200">
                <div className="bg-blue-500 h-[40%]"></div>
                <div className="bg-white h-[20%]"></div>
                <div className="bg-red-500 h-[40%]"></div>
              </div>
              <span className="text-sm font-medium">English</span>
            </DropdownMenuItem>
            <DropdownMenuItem className="cursor-pointer flex items-center gap-2 py-2">
              <div className="w-5 h-5 rounded overflow-hidden border border-gray-200">
                <div className="bg-red-600 w-full h-[33.3%]"></div>
                <div className="bg-white w-full h-[33.3%]"></div>
                <div className="bg-blue-600 w-full h-[33.3%]"></div>
              </div>
              <span className="text-sm font-medium">Nederlands</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </aside>
    </>
  );
};

export default Sidebar;
