import React, { useState, useEffect } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Loader2, AlertCircle } from 'lucide-react';
import { getUserEntity, getEntityUsers, getEntityById } from '@/features/entities/api/profileHelpers';
import { getProfileById } from '@/features/entities/api/profileHelpers';
import { getArtistDetails } from '@/features/entities/api/artistHelpers';
import { getVenueProfile, getAgencyProfile } from '@/features/entities/api/entityProfileHelpers';
import PersonalProfileTab from './PersonalProfileTab';
import EntityProfileTab from './EntityProfileTab';

interface ProfilePageProps {
  userType: 'artist' | 'venue' | 'agency';
  children?: React.ReactNode;
}

const ProfilePage = ({ userType, children }: ProfilePageProps) => {
  const { profile, user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [entityUsers, setEntityUsers] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState('entity');
  const [error, setError] = useState<string | null>(null);
  const [userData, setUserData] = useState<any>(null);
  const [entityData, setEntityData] = useState<any>(null);
  const [profileData, setProfileData] = useState<any>(null);

  const fetchProfileData = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        // Get user data
        const userProfileData = await getProfileById(user.id);
        if (userProfileData) {
          setUserData(userProfileData);
        }

        if (!profile || !profile.entity) {
          setLoading(false);
          return;
        }

        // Get entity data
        const entity = await getEntityById(profile.entity.id);
        if (entity) {
          setEntityData(entity);
        }

        // Get users associated with this entity
        const users = await getEntityUsers(profile.entity.id);
        if (users) {
          setEntityUsers(users);
        }

        // Get profile data based on entity type
        if (userType === 'artist') {
          const artistProfile = await getArtistDetails(profile.entity.id);
          if (artistProfile) {
            setProfileData(artistProfile);
          }
        } else if (userType === 'venue') {
          const venueProfile = await getVenueProfile(profile.entity.id);
          if (venueProfile) {
            setProfileData(venueProfile);
          }
        } else if (userType === 'agency') {
          const agencyProfile = await getAgencyProfile(profile.entity.id);
          if (agencyProfile) {
            setProfileData(agencyProfile);
          }
        }
      } catch (error) {
        console.error('Error fetching profile data:', error);
        setError("An error occurred while loading profile data.");
      } finally {
        setLoading(false);
      }
  };

  useEffect(() => {
    fetchProfileData();
  }, [user, profile, userType]);

  if (loading) {
    return (
      <DashboardLayout userType={userType}>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          <span className="ml-2 text-gray-500">Loading profile information...</span>
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout userType={userType}>
        <Card className="p-6">
          <div className="flex items-center text-red-500 gap-2">
            <AlertCircle className="h-5 w-5" />
            <p>{error}</p>
          </div>
        </Card>
      </DashboardLayout>
    );
  }

  const getDefaultEntityName = () => {
    if (userType === 'artist') return 'Artist Profile';
    if (userType === 'venue') return 'Venue Profile';
    if (userType === 'agency') return 'Agency Profile';
    return 'Entity Profile';
  };

  const entityName = profile?.entity?.name || getDefaultEntityName();

  return (
    <DashboardLayout userType={userType}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Profile Settings</h1>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="bg-zinc-200">
            <TabsTrigger value="entity">{entityName}</TabsTrigger>
            <TabsTrigger value="personal">Personal Profile</TabsTrigger>
          </TabsList>

          <TabsContent value="entity" className="space-y-6">
            {entityData && profileData ? (
              <EntityProfileTab
                entityType={userType}
                entityData={entityData}
                profileData={profileData}
                entityUsers={entityUsers}
                onEntityUpdated={() => {
                  // Refresh data after update
                  setLoading(true);
                  fetchProfileData();
                }}
              />
            ) : (
              <Card className="p-6">
                <div className="flex items-center justify-center py-8 text-gray-500">
                  <p>No entity data available.</p>
                </div>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="personal" className="space-y-6">
            {userData ? (
              <PersonalProfileTab
                userId={user!.id}
                userData={userData}
                onProfileUpdated={() => {
                  // Refresh data after update
                  setLoading(true);
                  fetchProfileData();
                }}
              />
            ) : (
              <Card className="p-6">
                <div className="flex items-center justify-center py-8 text-gray-500">
                  <p>No user data available.</p>
                </div>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default ProfilePage;
