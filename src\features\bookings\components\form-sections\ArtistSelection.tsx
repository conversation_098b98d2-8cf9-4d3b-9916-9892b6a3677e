import { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from "@/components/ui/badge";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription } from "@/components/ui/sheet";
import { X, Loader2, Search, Users, Mail } from 'lucide-react';
import { showErrorToast, showSuccessToast } from '@/core';
import { createArtist } from '@/features/entities/api';
import { ArtistData } from '@/features/entities/types';

interface ArtistSelectionProps {
  selectedArtist: ArtistData | null;
  setSelectedArtist: (artist: ArtistData | null) => void;
  artists: ArtistData[];
  agencyArtists?: ArtistData[]; // Agency's own artists
  userType?: string; // To determine if user is from an agency
  isLoadingArtists: boolean;
}

const ArtistSelection = ({
  selectedArtist,
  setSelectedArtist,
  artists,
  agencyArtists = [],
  userType,
  isLoadingArtists
}: ArtistSelectionProps) => {
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddArtist, setShowAddArtist] = useState(false);
  const [newArtistName, setNewArtistName] = useState('');
  const [newArtistEmail, setNewArtistEmail] = useState('');
  const [loading, setLoading] = useState(false);

  const filteredArtists = artists.filter(artist => {
    // If no search query, show all artists
    if (!searchQuery) return true;

    // Search by name (both artist_name and name fields)
    const nameMatch =
      (artist.artist_name && artist.artist_name.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (artist.name && artist.name.toLowerCase().includes(searchQuery.toLowerCase()));

    // Also search by email for placeholder artists
    const emailMatch = artist.is_placeholder &&
      artist.contact_email &&
      artist.contact_email.toLowerCase().includes(searchQuery.toLowerCase());

    return nameMatch || emailMatch;
  });

  const getArtistDisplayName = (artist: ArtistData) => {
    return artist.artist_name || artist.name || 'Unknown Artist';
  };

  const handleAddNewArtist = async () => {
    if (!newArtistName.trim()) {
      showErrorToast("Please enter an artist name");
      return;
    }

    setLoading(true);
    try {
      // Create a placeholder artist with email if provided
      const isPlaceholder = true;
      const newArtist = await createArtist(
        newArtistName,
        newArtistEmail.trim() || null,
        isPlaceholder
      );

      setSelectedArtist(newArtist);
      setShowAddArtist(false);
      setNewArtistName('');
      setNewArtistEmail('');
      showSuccessToast('New artist added successfully');
    } catch (error) {
      console.error('Error adding new artist:', error);
      showErrorToast('Failed to add new artist');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="grid gap-2">
      <Label htmlFor="artist" className="required">
        Artist Selection
      </Label>

      {!showAddArtist ? (
        <div className="space-y-2">
          <div className="flex gap-2">
            <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
              <Button
                variant="outline"
                className="w-full justify-between"
                onClick={() => setIsSheetOpen(true)}
              >
                {selectedArtist ? (
                  <span className="truncate">
                    {getArtistDisplayName(selectedArtist)}
                    {selectedArtist.is_placeholder && " (Placeholder)"}
                  </span>
                ) : (
                  <span className="text-muted-foreground">Select an artist</span>
                )}
                {selectedArtist && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 ml-2 hover:bg-gray-100 rounded-full"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedArtist(null);
                    }}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </Button>
              <SheetContent side="right" className="w-[300px] sm:w-[400px]">
                <SheetHeader>
                  <SheetTitle>Select an Artist</SheetTitle>
                  <SheetDescription>
                    Choose an existing artist or add a new one
                  </SheetDescription>
                </SheetHeader>
                <div className="py-4">
                  <div className="relative mb-4">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search artists..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-8"
                      autoFocus
                    />
                    {searchQuery && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 absolute right-1.5 top-1.5 hover:bg-gray-100"
                        onClick={() => setSearchQuery('')}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>

                  <div className="space-y-4 max-h-[50vh] overflow-y-auto pr-1">
                    {/* Agency Artists Section - Only shown for agency users */}
                    {userType === 'agency' && agencyArtists.length > 0 && (
                      <div className="space-y-2">
                        <h3 className="text-sm font-medium text-gray-500 flex items-center gap-1 mb-2">
                          <Users className="h-4 w-4" />
                          Your Artists
                        </h3>
                        <div className="space-y-1 border-l-2 border-blue-200 pl-2">
                          {agencyArtists
                            .filter(artist => {
                              // If no search query, show all artists
                              if (!searchQuery) return true;

                              // Search by name
                              const nameMatch = getArtistDisplayName(artist).toLowerCase().includes(searchQuery.toLowerCase());

                              // Also search by email for placeholder artists
                              const emailMatch = artist.is_placeholder &&
                                artist.contact_email &&
                                artist.contact_email.toLowerCase().includes(searchQuery.toLowerCase());

                              return nameMatch || emailMatch;
                            })
                            .map((artist) => {
                              const displayName = getArtistDisplayName(artist);
                              return (
                                <Button
                                  key={artist.id}
                                  variant="outline"
                                  className="w-full justify-between text-left h-auto py-3 border-blue-100 bg-blue-50 hover:bg-blue-100"
                                  onClick={() => {
                                    setSelectedArtist(artist);
                                    setIsSheetOpen(false);
                                    setSearchQuery('');
                                  }}
                                >
                                  <div className="flex items-center gap-2">
                                    <span className="font-medium">{displayName}</span>
                                  </div>
                                  {artist.is_placeholder && (
                                    <Badge variant="secondary" className="bg-amber-100 text-amber-800 hover:bg-amber-100 text-xs font-medium">
                                      {artist.contact_email ? 'Placeholder' : 'Placeholder'}
                                    </Badge>
                                  )}
                                </Button>
                              );
                            })}
                        </div>
                      </div>
                    )}

                    {/* All Artists Section */}
                    <div className="space-y-2">
                      {userType === 'agency' && agencyArtists.length > 0 && (
                        <h3 className="text-sm font-medium text-gray-500 mb-2">All Artists</h3>
                      )}

                      {isLoadingArtists ? (
                        <div className="flex items-center justify-center p-4">
                          <Loader2 className="h-6 w-6 animate-spin text-gray-500 mr-2" />
                          <span>Loading artists...</span>
                        </div>
                      ) : filteredArtists.length > 0 ? (
                        filteredArtists.map((artist) => {
                          const displayName = getArtistDisplayName(artist);
                          return (
                            <Button
                              key={artist.id}
                              variant="outline"
                              className="w-full justify-between text-left h-auto py-3"
                              onClick={() => {
                                setSelectedArtist(artist);
                                setIsSheetOpen(false);
                                setSearchQuery('');
                              }}
                            >
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{displayName}</span>
                              </div>
                              {artist.is_placeholder && (
                                <Badge variant="secondary" className="bg-amber-100 text-amber-800 hover:bg-amber-100 text-xs font-medium">
                                  {artist.contact_email ? 'Placeholder' : 'Placeholder'}
                                </Badge>
                              )}
                            </Button>
                          );
                        })
                      ) : (
                        <div className="p-4 text-center text-sm text-muted-foreground border rounded-md">
                          <Users className="h-10 w-10 mx-auto mb-2 text-gray-400" />
                          <p>No artists found</p>
                          <p className="mt-1 text-xs">Try a different search or add a new artist</p>
                        </div>
                      )}
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    className="w-full mt-4 flex items-center justify-center border-dashed"
                    onClick={() => {
                      setShowAddArtist(true);
                      setIsSheetOpen(false);
                    }}
                  >
                    Add New Artist
                  </Button>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      ) : (
        <div className="border border-gray-200 rounded-md p-3">
          <h4 className="text-sm font-medium mb-3">Add New Artist</h4>
          <div className="space-y-3">
            <div>
              <Input
                placeholder="Artist name"
                value={newArtistName}
                onChange={(e) => setNewArtistName(e.target.value)}
                autoFocus
              />
            </div>
            <div className="relative">
              <Mail className="h-4 w-4 absolute left-2.5 top-2.5 text-muted-foreground" />
              <Input
                placeholder="Email (optional)"
                value={newArtistEmail}
                onChange={(e) => setNewArtistEmail(e.target.value)}
                type="email"
                className="pl-8"
              />
            </div>
            <p className="text-xs text-muted-foreground">
              This creates a placeholder artist that can claim their profile later.
            </p>
          </div>
          <div className="flex justify-end gap-2 mt-3">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowAddArtist(false)}
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleAddNewArtist}
              disabled={loading || !newArtistName.trim()}
              className="bg-black hover:bg-gray-900"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-1" />
              ) : null}
              Create Artist
            </Button>
          </div>
        </div>
      )}

      {selectedArtist && (
        <div className="mt-2 border border-gray-200 rounded-md p-2 bg-gray-50">
          <div className="flex items-center justify-between">
            <span className="font-medium">{getArtistDisplayName(selectedArtist)}</span>
            {selectedArtist.is_placeholder && (
              <Badge variant="secondary" className="bg-amber-100 text-amber-800 font-medium">Placeholder</Badge>
            )}
          </div>
          {selectedArtist.contact_email && (
            <div className="text-sm text-gray-600 mt-1">
              <Mail className="h-4 w-4 text-gray-400 inline mr-1" />
              {selectedArtist.contact_email}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default ArtistSelection;
