import { supabase } from '@/core/api/supabase';

/**
 * Get venues associated with a user
 */
export const getUserVenues = async (userId: string) => {
  try {
    // First ensure we have a valid session
    const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error('Error getting session in getUserVenues:', sessionError);
      return null;
    }

    if (!sessionData.session) {
      console.error('No active session found in getUserVenues');
      return null;
    }

    console.log('Fetching venues for user ID:', userId);

    // Get user's entities that are venues
    const { data: entityData, error: entityError } = await supabase
      .from('user_with_entities')
      .select('*')
      .eq('user_id', userId)
      .eq('entity_type', 'venue');

    if (entityError) {
      console.error('Error fetching user venues:', entityError);
      return null;
    }

    if (!entityData || entityData.length === 0) {
      console.log('No venues found for user:', userId);
      return [];
    }

    console.log('Found venues for user:', entityData.length);

    // Transform the data to match the expected format
    return entityData.map(item => ({
      entity_id: item.entity_id, // Add entity_id for compatibility
      venue_id: item.entity_id,
      venue_name: item.entity_name || 'Unknown Venue',
      is_primary: item.is_primary
    }));
  } catch (error) {
    console.error('Exception in getUserVenues:', error);
    return null;
  }
};

/**
 * Get users associated with a venue
 */
export const getVenueUsers = async (entityId: string) => {
  try {
    // Get all users associated with this entity
    const { data: entityUsers, error: entityError } = await supabase
      .from('user_with_entities')
      .select('*')
      .eq('entity_id', entityId);

    if (entityError) {
      console.error('Error fetching venue users:', entityError);
      return null;
    }

    // Transform the data to match the expected format
    return entityUsers?.map(item => ({
      user_id: item.user_id,
      is_primary: item.is_primary,
      role: item.role,
      name: item.user_name,
      email: item.email,
      phone_number: item.phone_number,
      user_type: 'venue' // All users associated with a venue entity are considered venue users
    })) || null;
  } catch (error) {
    console.error('Exception in getVenueUsers:', error);
    return null;
  }
};

/**
 * Get a specific user associated with a venue
 */
export const getVenueUser = async (entityId: string, userId: string) => {
  try {
    const users = await getVenueUsers(entityId);
    if (!users) return null;

    return users.find(user => user.user_id === userId) || null;
  } catch (error) {
    console.error('Exception in getVenueUser:', error);
    return null;
  }
};
