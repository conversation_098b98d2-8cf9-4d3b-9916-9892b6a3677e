import { supabase } from '@/core/api/supabase';
import type {
  AgencyClient,
  AgencyClientContact,
  AgencyClientWithContacts,
  CreateAgencyClientData,
  CreateAgencyClientContactData,
  UpdateAgencyClientData,
  UpdateAgencyClientContactData,
  ClientFilters,
  ClientListResponse,
  ClientTag
} from '../types';

/**
 * Get all clients for an agency
 */
export const getAgencyClients = async (
  agencyEntityId: string,
  filters?: ClientFilters,
  page: number = 1,
  limit: number = 10
): Promise<ClientListResponse> => {
  try {
    let query = (supabase as any)
      .from('agency_clients_with_contacts')
      .select('*', { count: 'exact' })
      .eq('agency_entity_id', agencyEntityId);

    // Apply filters
    if (filters?.search) {
      query = query.ilike('name', `%${filters.search}%`);
    }

    if (filters?.type && filters.type !== 'all') {
      query = query.eq('type', filters.type);
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    // Order by name
    query = query.order('name');

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching agency clients:', error);
      throw error;
    }

    // Deduplicate contacts and tags for each client
    const deduplicatedClients = (data || []).map((client: any) => {
      const uniqueContacts = client.contacts ?
        client.contacts.filter((contact: any, index: number, self: any[]) =>
          index === self.findIndex((c: any) => c.id === contact.id)
        ) : [];

      const uniqueTags = client.tags ?
        client.tags.filter((tag: any, index: number, self: any[]) =>
          index === self.findIndex((t: any) => t.id === tag.id)
        ) : [];

      return {
        ...client,
        contacts: uniqueContacts,
        tags: uniqueTags
      };
    });

    return {
      clients: deduplicatedClients as AgencyClientWithContacts[],
      total: count || 0,
      page,
      limit
    };
  } catch (error) {
    console.error('Exception in getAgencyClients:', error);
    throw error;
  }
};

/**
 * Get a single client by ID
 */
export const getAgencyClient = async (clientId: string): Promise<AgencyClientWithContacts | null> => {
  try {
    const { data, error } = await (supabase as any)
      .from('agency_clients_with_contacts')
      .select('*')
      .eq('id', clientId)
      .single();

    if (error) {
      console.error('Error fetching agency client:', error);
      throw error;
    }

    if (!data) {
      return null;
    }

    // Deduplicate contacts and tags that might be duplicated due to the SQL view joins
    const uniqueContacts = data.contacts ?
      data.contacts.filter((contact: any, index: number, self: any[]) =>
        index === self.findIndex((c: any) => c.id === contact.id)
      ) : [];

    const uniqueTags = data.tags ?
      data.tags.filter((tag: any, index: number, self: any[]) =>
        index === self.findIndex((t: any) => t.id === tag.id)
      ) : [];

    return {
      ...data,
      contacts: uniqueContacts,
      tags: uniqueTags
    } as AgencyClientWithContacts;
  } catch (error) {
    console.error('Exception in getAgencyClient:', error);
    throw error;
  }
};

/**
 * Create a new client
 */
export const createAgencyClient = async (clientData: CreateAgencyClientData): Promise<AgencyClient> => {
  try {
    // Create the client
    const { data: client, error: clientError } = await supabase
      .from('agency_clients')
      .insert({
        agency_entity_id: clientData.agency_entity_id,
        name: clientData.name,
        type: clientData.type,
        email: clientData.email || null,
        phone: clientData.phone || null,
        address: clientData.address || null,
        vat_number: clientData.vat_number || null,
        notes: clientData.notes || null
      })
      .select()
      .single();

    if (clientError) {
      console.error('Error creating agency client:', clientError);
      throw clientError;
    }

    // Create contacts if provided
    if (clientData.contacts && clientData.contacts.length > 0) {
      const contactsToInsert = clientData.contacts.map(contact => ({
        client_id: client.id,
        name: contact.name,
        email: contact.email || null,
        phone: contact.phone || null,
        position: contact.position || null,
        is_primary: contact.is_primary || false
      }));

      const { error: contactsError } = await supabase
        .from('agency_client_contacts')
        .insert(contactsToInsert);

      if (contactsError) {
        console.error('Error creating client contacts:', contactsError);
        // Note: We don't throw here as the client was created successfully
      }
    }

    return client;
  } catch (error) {
    console.error('Exception in createAgencyClient:', error);
    throw error;
  }
};

/**
 * Update a client
 */
export const updateAgencyClient = async (
  clientId: string,
  updates: UpdateAgencyClientData
): Promise<AgencyClient> => {
  try {
    const { data, error } = await supabase
      .from('agency_clients')
      .update(updates)
      .eq('id', clientId)
      .select()
      .single();

    if (error) {
      console.error('Error updating agency client:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Exception in updateAgencyClient:', error);
    throw error;
  }
};

/**
 * Delete a client
 */
export const deleteAgencyClient = async (clientId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('agency_clients')
      .delete()
      .eq('id', clientId);

    if (error) {
      console.error('Error deleting agency client:', error);
      throw error;
    }
  } catch (error) {
    console.error('Exception in deleteAgencyClient:', error);
    throw error;
  }
};

/**
 * Create a new contact for a client
 */
export const createClientContact = async (
  clientId: string,
  contactData: CreateAgencyClientContactData
): Promise<AgencyClientContact> => {
  try {
    const { data, error } = await supabase
      .from('agency_client_contacts')
      .insert({
        client_id: clientId,
        name: contactData.name,
        email: contactData.email || null,
        phone: contactData.phone || null,
        position: contactData.position || null,
        is_primary: contactData.is_primary || false
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating client contact:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Exception in createClientContact:', error);
    throw error;
  }
};

/**
 * Update a contact
 */
export const updateClientContact = async (
  contactId: string,
  updates: UpdateAgencyClientContactData
): Promise<AgencyClientContact> => {
  try {
    const { data, error } = await supabase
      .from('agency_client_contacts')
      .update(updates)
      .eq('id', contactId)
      .select()
      .single();

    if (error) {
      console.error('Error updating client contact:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Exception in updateClientContact:', error);
    throw error;
  }
};

/**
 * Delete a contact
 */
export const deleteClientContact = async (contactId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('agency_client_contacts')
      .delete()
      .eq('id', contactId);

    if (error) {
      console.error('Error deleting client contact:', error);
      throw error;
    }
  } catch (error) {
    console.error('Exception in deleteClientContact:', error);
    throw error;
  }
};

/**
 * Get contacts for a specific client
 */
export const getClientContacts = async (clientId: string): Promise<AgencyClientContact[]> => {
  try {
    const { data, error } = await supabase
      .from('agency_client_contacts')
      .select('*')
      .eq('client_id', clientId)
      .order('is_primary', { ascending: false })
      .order('name');

    if (error) {
      console.error('Error fetching client contacts:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Exception in getClientContacts:', error);
    throw error;
  }
};

/**
 * Get all tags for an agency
 */
export const getAgencyTags = async (agencyEntityId: string): Promise<ClientTag[]> => {
  try {
    const { data, error } = await supabase
      .from('client_tags')
      .select('*')
      .eq('agency_entity_id', agencyEntityId)
      .order('name');

    if (error) {
      console.error('Error fetching agency tags:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Exception in getAgencyTags:', error);
    throw error;
  }
};

/**
 * Create a new tag
 */
export const createClientTag = async (
  agencyEntityId: string,
  name: string,
  color: string = '#6B7280'
): Promise<ClientTag> => {
  try {
    const { data, error } = await supabase
      .from('client_tags')
      .insert({
        agency_entity_id: agencyEntityId,
        name: name.trim(),
        color
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating client tag:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Exception in createClientTag:', error);
    throw error;
  }
};

/**
 * Assign tags to a client
 */
export const assignTagsToClient = async (clientId: string, tagIds: string[]): Promise<void> => {
  try {
    // First, remove existing tag assignments
    await supabase
      .from('client_tag_assignments')
      .delete()
      .eq('client_id', clientId);

    // Then, add new assignments if any
    if (tagIds.length > 0) {
      const assignments = tagIds.map(tagId => ({
        client_id: clientId,
        tag_id: tagId
      }));

      const { error } = await supabase
        .from('client_tag_assignments')
        .insert(assignments);

      if (error) {
        console.error('Error assigning tags to client:', error);
        throw error;
      }
    }
  } catch (error) {
    console.error('Exception in assignTagsToClient:', error);
    throw error;
  }
};

/**
 * Delete a tag (and all its assignments)
 */
export const deleteClientTag = async (tagId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('client_tags')
      .delete()
      .eq('id', tagId);

    if (error) {
      console.error('Error deleting client tag:', error);
      throw error;
    }
  } catch (error) {
    console.error('Exception in deleteClientTag:', error);
    throw error;
  }
};
