
import { Link } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { 
  Calendar, 
  FileText, 
  DollarSign, 
  Users, 
  Building2,
  Music,
  ChevronRight,
  CheckCircle,
  ArrowRight,
  Euro
} from 'lucide-react';
import Footer from '@/components/layout/Footer';

const Landing = () => {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Simple Header */}
      <header className="border-b border-gray-100 py-6">
        <div className="container mx-auto px-4 md:px-6 flex justify-between items-center">
          <Link to="/" className="flex items-center">
            <div className="flex items-center">
              <span className="text-2xl font-bold text-stagecloud-black">StageCloud.</span>
            </div>
          </Link>
          
          <nav className="hidden md:flex items-center space-x-8">
            <Link to="/about" className="text-gray-600 hover:text-stagecloud-black transition-colors">
              Voor venues
            </Link>
            <Link to="/features" className="text-gray-600 hover:text-stagecloud-black transition-colors">
              Voor DJ's
            </Link>
            <Link to="/contact" className="text-gray-600 hover:text-stagecloud-black transition-colors">
              Contact
            </Link>
            <Button asChild className="bg-stagecloud-black text-white hover:bg-stagecloud-gray-800 rounded-md">
              <Link to="/register">Registreer</Link>
            </Button>
          </nav>
        </div>
      </header>
      
      {/* Hero Section */}
      <section className="py-20 flex-grow">
        <div className="container mx-auto px-4 md:px-6">
          {/* DJ Button */}
          <div className="flex justify-center mb-10">
            <Link to="/artist/register" className="inline-flex items-center px-4 py-2 bg-gray-100 rounded-full text-sm text-gray-700 hover:bg-gray-200 transition-colors">
              Ben jij DJ? Blijf op de hoogte! <ArrowRight className="w-4 h-4 ml-2" />
            </Link>
          </div>
          
          {/* Hero Content */}
          <div className="text-center max-w-4xl mx-auto mb-16">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-stagecloud-black mb-8">
              Boek en beheer DJ's moeiteloos.
            </h1>
            <p className="text-xl text-stagecloud-gray-600 mb-12 max-w-3xl mx-auto">
              StageCloud is dé snelste en overzichtelijkste manier om DJ's te boeken en je planning te 
              beheren. Geen losse berichten, geen papierwerk, alles op één plek.
            </p>
            
            {/* Email Registration Form */}
            <div className="flex flex-col md:flex-row items-center justify-center gap-4 max-w-3xl mx-auto">
              <Select>
                <SelectTrigger className="w-full md:w-48 bg-white border border-gray-200">
                  <SelectValue placeholder="Ik ben een..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="venue">Venue</SelectItem>
                  <SelectItem value="artist">Artist/DJ</SelectItem>
                </SelectContent>
              </Select>
              
              <Input 
                type="email" 
                placeholder="<EMAIL>" 
                className="w-full md:w-auto flex-grow bg-white border border-gray-200"
              />
              
              <Button className="w-full md:w-auto bg-stagecloud-black hover:bg-stagecloud-gray-800">
                Pre-registreer
              </Button>
            </div>
          </div>
          
          {/* Booking Demo Card */}
          <div className="relative max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-xl p-4 border border-stagecloud-gray-200">
              <div className="flex items-center border-b pb-4 mb-4">
                <div className="h-10 w-10 rounded-md bg-stagecloud-gray-100 flex items-center justify-center text-stagecloud-black">
                  <Calendar className="h-6 w-6" />
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-semibold">New Booking Request</h3>
                  <p className="text-sm text-stagecloud-gray-600">The Blue Note · Friday, April 18, 2025</p>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-stagecloud-gray-600">Artist:</span>
                  <span className="font-medium">The Groove Band</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-stagecloud-gray-600">Time:</span>
                  <span className="font-medium">8:00 PM - 11:00 PM</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-stagecloud-gray-600">Fee:</span>
                  <span className="font-medium">$1,200.00</span>
                </div>
              </div>
              <div className="mt-4 flex justify-end space-x-2">
                <Button variant="outline" size="sm">Decline</Button>
                <Button size="sm" className="bg-stagecloud-black hover:bg-stagecloud-gray-800">Accept</Button>
              </div>
            </div>
            <div className="absolute -bottom-6 -right-6 bg-stagecloud-gray-800 rounded-lg shadow-xl p-4 border border-stagecloud-gray-900 w-64 z-10">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-white">Contract Generated</span>
                <CheckCircle className="h-5 w-5 text-white" />
              </div>
              <p className="text-xs text-white/80">Ready for digital signatures</p>
            </div>
          </div>
        </div>
      </section>
      
      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-stagecloud-black mb-4">
              Everything You Need to Manage Bookings
            </h2>
            <p className="text-xl text-stagecloud-gray-700 max-w-3xl mx-auto">
              A comprehensive platform designed specifically for the unique needs of venues and artists.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white rounded-lg p-6 shadow-lg border border-stagecloud-gray-100">
              <div className="h-12 w-12 rounded-md bg-stagecloud-gray-100 flex items-center justify-center text-stagecloud-black mb-4">
                <Calendar className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-bold mb-2">Smart Calendar</h3>
              <p className="text-stagecloud-gray-600 mb-4">
                Manage bookings with an intuitive calendar interface that prevents double-bookings.
              </p>
              <Link to="/features" className="text-stagecloud-black font-medium inline-flex items-center">
                Learn more <ChevronRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-lg border border-stagecloud-gray-100">
              <div className="h-12 w-12 rounded-md bg-stagecloud-gray-100 flex items-center justify-center text-stagecloud-black mb-4">
                <FileText className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-bold mb-2">Contract Generation</h3>
              <p className="text-stagecloud-gray-600 mb-4">
                Create professional contracts automatically based on booking details.
              </p>
              <Link to="/features" className="text-stagecloud-black font-medium inline-flex items-center">
                Learn more <ChevronRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-lg border border-stagecloud-gray-100">
              <div className="h-12 w-12 rounded-md bg-stagecloud-gray-100 flex items-center justify-center text-stagecloud-black mb-4">
                <Euro className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-bold mb-2">Financial Tracking</h3>
              <p className="text-stagecloud-gray-600 mb-4">
                Keep track of payments, fees, and financial performance over time.
              </p>
              <Link to="/features" className="text-stagecloud-black font-medium inline-flex items-center">
                Learn more <ChevronRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-lg border border-stagecloud-gray-100">
              <div className="h-12 w-12 rounded-md bg-stagecloud-gray-100 flex items-center justify-center text-stagecloud-black mb-4">
                <Users className="h-6 w-6" />
              </div>
              <h3 className="text-xl font-bold mb-2">Staff Management</h3>
              <p className="text-stagecloud-gray-600 mb-4">
                Assign roles and permissions to venue staff for efficient operations.
              </p>
              <Link to="/features" className="text-stagecloud-black font-medium inline-flex items-center">
                Learn more <ChevronRight className="h-4 w-4 ml-1" />
              </Link>
            </div>
          </div>
        </div>
      </section>
      
      {/* CTA Section */}
      <section className="bg-stagecloud-black text-white py-16">
        <div className="container mx-auto px-4 md:px-6">
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              Ready to Streamline Your Booking Process?
            </h2>
            <p className="text-xl opacity-90 mb-8">
              Join venues and artists already using StageCloud to manage their bookings and events.
            </p>
            <div className="flex flex-col md:flex-row justify-center gap-4">
              <Button className="bg-white text-stagecloud-black hover:bg-stagecloud-gray-100 text-lg px-8 py-6" asChild>
                <Link to="/register">
                  <Building2 className="h-5 w-5 mr-2" />
                  I'm a Venue
                </Link>
              </Button>
              <Button className="bg-stagecloud-gray-800 text-white hover:bg-stagecloud-gray-700 text-lg px-8 py-6" asChild>
                <Link to="/register">
                  <Music className="h-5 w-5 mr-2" />
                  I'm an Artist
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
      
      <Footer />
    </div>
  );
};

export default Landing;
