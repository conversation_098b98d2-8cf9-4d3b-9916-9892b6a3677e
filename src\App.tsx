import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";

// Pages
import Login from "./pages/Login";
import Register from "./pages/Register";
import NotFound from "./pages/NotFound";
import DemoAccounts from "./pages/DemoAccounts";
import BookingDetailsRedirect from "./pages/BookingDetailsRedirect";
import DocumentView from "@/pages/DocumentView";
import PublicDocumentView from "@/pages/PublicDocumentView";

// Template Pages
import TemplateEditor from "./pages/templates/TemplateEditor";
import TemplateList from "./pages/templates/TemplateList";

// Venue Pages
import VenueDashboard from "./pages/venue/Dashboard";
import VenueBookings from "./pages/venue/Bookings";
import VenueBookingDetails from "./pages/venue/BookingDetails";
import VenuePaperwork from "./pages/venue/Paperwork";
import VenueProfile from "./pages/venue/Profile";
import VenueCalendar from "./pages/venue/Calendar";
import VenueSettings from "./pages/venue/Settings";

// Artist Pages
import ArtistDashboard from "./pages/artist/Dashboard";
import ArtistBookings from "./pages/artist/Bookings";
import ArtistBookingDetails from "./pages/artist/BookingDetails";
import ArtistPaperwork from "./pages/artist/Paperwork";
import ArtistProfile from "./pages/artist/Profile";
import ArtistCalendar from "./pages/artist/Calendar";
import ArtistSettings from "./pages/artist/Settings";

// Agency Pages
import AgencyDashboard from "./pages/agency/Dashboard";
import AgencyBookings from "./pages/agency/Bookings";
import AgencyBookingDetails from "./pages/agency/BookingDetails";
import AgencyPaperwork from "./pages/agency/Paperwork";
import AgencyProfile from "./pages/agency/Profile";
import AgencyCalendar from "./pages/agency/Calendar";
import AgencySettings from "./pages/agency/Settings";
import AgencyClients from "./pages/agency/Clients";
import AgencyClientDetails from "./pages/agency/ClientDetails";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <BrowserRouter>
        <AuthProvider>
          <Toaster />
          <Routes>
            {/* Public Routes - Redirect from landing to login */}
            <Route path="/" element={<Navigate to="/login" replace />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/demo-accounts" element={<DemoAccounts />} />

            {/* Public Document Sharing Route */}
            <Route path="/shared/documents/:shareId" element={<PublicDocumentView />} />

            {/* Booking Details - Redirect to entity-specific routes */}
            <Route
              path="/bookings/:id"
              element={
                <ProtectedRoute>
                  {/* This component will redirect to the appropriate entity-specific booking details page */}
                  <BookingDetailsRedirect />
                </ProtectedRoute>
              }
            />

            {/* Venue Routes - Protected */}
            <Route
              path="/venue/dashboard"
              element={
                <ProtectedRoute userType="venue">
                  <VenueDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/venue/bookings"
              element={
                <ProtectedRoute userType="venue">
                  <VenueBookings />
                </ProtectedRoute>
              }
            />
            <Route
              path="/venue/bookings/:id"
              element={
                <ProtectedRoute userType="venue">
                  <VenueBookingDetails />
                </ProtectedRoute>
              }
            />
            <Route
              path="/venue/paperwork"
              element={
                <ProtectedRoute userType="venue">
                  <VenuePaperwork />
                </ProtectedRoute>
              }
            />
            <Route
              path="/venue/profile"
              element={
                <ProtectedRoute userType="venue">
                  <VenueProfile />
                </ProtectedRoute>
              }
            />
            <Route
              path="/venue/calendar"
              element={
                <ProtectedRoute userType="venue">
                  <VenueCalendar />
                </ProtectedRoute>
              }
            />
            <Route
              path="/venue/settings"
              element={
                <ProtectedRoute userType="venue">
                  <VenueSettings />
                </ProtectedRoute>
              }
            />

            {/* Artist Routes - Protected */}
            <Route
              path="/artist/dashboard"
              element={
                <ProtectedRoute userType="artist">
                  <ArtistDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/artist/bookings"
              element={
                <ProtectedRoute userType="artist">
                  <ArtistBookings />
                </ProtectedRoute>
              }
            />
            <Route
              path="/artist/bookings/:id"
              element={
                <ProtectedRoute userType="artist">
                  <ArtistBookingDetails />
                </ProtectedRoute>
              }
            />
            <Route
              path="/artist/paperwork"
              element={
                <ProtectedRoute userType="artist">
                  <ArtistPaperwork />
                </ProtectedRoute>
              }
            />
            <Route
              path="/artist/profile"
              element={
                <ProtectedRoute userType="artist">
                  <ArtistProfile />
                </ProtectedRoute>
              }
            />
            <Route
              path="/artist/calendar"
              element={
                <ProtectedRoute userType="artist">
                  <ArtistCalendar />
                </ProtectedRoute>
              }
            />
            <Route
              path="/artist/settings"
              element={
                <ProtectedRoute userType="artist">
                  <ArtistSettings />
                </ProtectedRoute>
              }
            />

            {/* Document View Route */}
            <Route
              path="/documents/:id"
              element={
                <ProtectedRoute>
                  <DocumentView />
                </ProtectedRoute>
              }
            />

            {/* Agency Routes - Protected */}
            <Route
              path="/agency/dashboard"
              element={
                <ProtectedRoute userType="agency">
                  <AgencyDashboard />
                </ProtectedRoute>
              }
            />

            <Route
              path="/agency/bookings"
              element={
                <ProtectedRoute userType="agency">
                  <AgencyBookings />
                </ProtectedRoute>
              }
            />
            <Route
              path="/agency/bookings/:id"
              element={
                <ProtectedRoute userType="agency">
                  <AgencyBookingDetails />
                </ProtectedRoute>
              }
            />
            <Route
              path="/agency/paperwork"
              element={
                <ProtectedRoute userType="agency">
                  <AgencyPaperwork />
                </ProtectedRoute>
              }
            />
            <Route
              path="/agency/profile"
              element={
                <ProtectedRoute userType="agency">
                  <AgencyProfile />
                </ProtectedRoute>
              }
            />
            <Route
              path="/agency/calendar"
              element={
                <ProtectedRoute userType="agency">
                  <AgencyCalendar />
                </ProtectedRoute>
              }
            />
            <Route
              path="/agency/settings"
              element={
                <ProtectedRoute userType="agency">
                  <AgencySettings />
                </ProtectedRoute>
              }
            />
            <Route
              path="/agency/clients"
              element={
                <ProtectedRoute userType="agency">
                  <AgencyClients />
                </ProtectedRoute>
              }
            />
            <Route
              path="/agency/clients/:id"
              element={
                <ProtectedRoute userType="agency">
                  <AgencyClientDetails />
                </ProtectedRoute>
              }
            />

            {/* Template Routes */}
            <Route
              path="/templates"
              element={
                <ProtectedRoute>
                  <TemplateList />
                </ProtectedRoute>
              }
            />
            <Route
              path="/templates/:id"
              element={
                <ProtectedRoute>
                  <TemplateEditor />
                </ProtectedRoute>
              }
            />

            {/* Catch-all route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
