import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Building2, User } from 'lucide-react';
import { TagInput } from '../index';
import type { ClientTag } from '../../types';

interface BasicInformationProps {
  name: string;
  setName: (name: string) => void;
  type: 'company' | 'person';
  setType: (type: 'company' | 'person') => void;
  email: string;
  setEmail: (email: string) => void;
  phone: string;
  setPhone: (phone: string) => void;
  address: string;
  setAddress: (address: string) => void;
  vatNumber: string;
  setVatNumber: (vatNumber: string) => void;
  notes: string;
  setNotes: (notes: string) => void;
  selectedTags: ClientTag[];
  availableTags: ClientTag[];
  onTagsChange: (tags: ClientTag[]) => void;
  onCreateTag: (name: string) => Promise<ClientTag>;
}

const BasicInformation = ({
  name,
  setName,
  type,
  setType,
  email,
  setEmail,
  phone,
  setPhone,
  address,
  setAddress,
  vatNumber,
  setVatNumber,
  notes,
  setNotes,
  selectedTags,
  availableTags,
  onTagsChange,
  onCreateTag
}: BasicInformationProps) => {
  return (
    <>
      <h3 className="text-lg font-semibold">Basic Information</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="grid gap-2">
          <Label htmlFor="clientName" className="required">
            Client Name
          </Label>
          <Input
            id="clientName"
            placeholder="Enter client name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
          />
        </div>

        <div className="grid gap-2">
          <Label htmlFor="clientType" className="required">
            Client Type
          </Label>
          <Select value={type} onValueChange={(value: 'company' | 'person') => setType(value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select client type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="company">
                <div className="flex items-center">
                  <Building2 className="h-4 w-4 mr-2" />
                  Company
                </div>
              </SelectItem>
              <SelectItem value="person">
                <div className="flex items-center">
                  <User className="h-4 w-4 mr-2" />
                  Individual
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="grid gap-2">
          <Label htmlFor="clientEmail">
            Email Address
          </Label>
          <Input
            id="clientEmail"
            type="email"
            placeholder="<EMAIL>"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
        </div>

        <div className="grid gap-2">
          <Label htmlFor="clientPhone">
            Phone Number
          </Label>
          <Input
            id="clientPhone"
            type="tel"
            placeholder="+****************"
            value={phone}
            onChange={(e) => setPhone(e.target.value)}
          />
        </div>
      </div>

      <div className="grid gap-2">
        <Label htmlFor="clientAddress">
          Address
        </Label>
        <Textarea
          id="clientAddress"
          placeholder="Enter full address"
          value={address}
          onChange={(e) => setAddress(e.target.value)}
          rows={2}
        />
      </div>

      {type === 'company' && (
        <div className="grid gap-2">
          <Label htmlFor="vatNumber">
            VAT Number
          </Label>
          <Input
            id="vatNumber"
            placeholder="Enter VAT number"
            value={vatNumber}
            onChange={(e) => setVatNumber(e.target.value)}
          />
        </div>
      )}

      <div className="grid gap-2">
        <Label htmlFor="clientNotes">
          Notes
        </Label>
        <Textarea
          id="clientNotes"
          placeholder="Additional notes about the client..."
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          rows={3}
        />
      </div>

      <div className="grid gap-2">
        <Label>
          Tags
        </Label>
        <TagInput
          selectedTags={selectedTags}
          availableTags={availableTags}
          onTagsChange={onTagsChange}
          onCreateTag={onCreateTag}
          placeholder="Add tags to categorize this client..."
        />
      </div>
    </>
  );
};

export default BasicInformation;
