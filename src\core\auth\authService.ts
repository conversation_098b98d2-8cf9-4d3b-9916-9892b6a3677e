import { supabase } from '@/core/api/supabase';
import { UserProfile } from '@/core/types/auth';
import { profileService } from '@/features/entities/api';

/**
 * Sign out the current user
 */
export const signOut = async () => {
  return await supabase.auth.signOut();
};

/**
 * Get the current session
 */
export const getSession = async () => {
  return await supabase.auth.getSession();
};

/**
 * Get the current user
 */
export const getUser = async () => {
  const { data } = await supabase.auth.getUser();
  return data.user;
};

/**
 * Set up an auth state change listener
 */
export const onAuthStateChange = (callback: (event: string, session: any) => void) => {
  return supabase.auth.onAuthStateChange(callback);
};

/**
 * Fetch the user profile
 */
export const fetchUserProfile = async (userId: string): Promise<UserProfile | null> => {
  try {
    // Get user entity
    const userEntity = await profileService.getUserEntity(userId);
    
    // Get user profile
    const userData = await profileService.getProfileById(userId);

    if (!userData) {
      // Get user metadata from auth
      const { data: authData } = await supabase.auth.getUser();
      const authUser = authData?.user;

      // Determine user type from entity or metadata
      let userType: 'artist' | 'venue' | 'agency' = 'venue'; // Default to venue

      if (userEntity) {
        userType = userEntity.entity_type as 'artist' | 'venue' | 'agency';
      } else if (authUser?.user_metadata?.user_type) {
        userType = authUser.user_metadata.user_type as 'artist' | 'venue' | 'agency';
      } else {
        // Check localStorage as last resort
        const storedType = localStorage.getItem('user_type');
        if (storedType === 'artist' || storedType === 'venue' || storedType === 'agency') {
          userType = storedType as 'artist' | 'venue' | 'agency';
        }
      }

      // Store the determined user type
      localStorage.setItem('user_type', userType);

      // Return minimal profile
      return {
        id: userId,
        email: authUser?.email || '',
        name: authUser?.user_metadata?.name || 'User',
        user_type: userType,
        phone_number: authUser?.phone || null,
        profile_image_url: null
      };
    }

    // Determine user type
    let userType: 'artist' | 'venue' | 'agency';

    if (userEntity) {
      userType = userEntity.entity_type as 'artist' | 'venue' | 'agency';
    } else if (authData?.user?.user_metadata?.user_type) {
      userType = authData.user.user_metadata.user_type as 'artist' | 'venue' | 'agency';
    } else {
      // Check localStorage as last resort
      const storedType = localStorage.getItem('user_type');
      if (storedType === 'artist' || storedType === 'venue' || storedType === 'agency') {
        userType = storedType as 'artist' | 'venue' | 'agency';
      } else {
        // Default to venue if nothing else is available
        userType = 'venue';
      }
    }

    // Store the determined user type
    localStorage.setItem('user_type', userType);

    let userProfile: UserProfile = {
      id: userData.id,
      email: userData.email,
      name: userData.name,
      phone_number: userData.phone_number,
      profile_image_url: userData.profile_image_url,
      user_type: userType
    };

    // If we found an entity relationship, add it to the profile
    if (userEntity && userEntity.entity_id) {
      userProfile.entity = {
        id: userEntity.entity_id,
        type: userEntity.entity_type as 'artist' | 'venue' | 'agency',
        name: userEntity.entity_name,
        is_primary: userEntity.is_primary
      };

      // Get entity-specific details based on the entity type
      if (userEntity.entity_type === 'artist') {
        userProfile.user_type = 'artist';
        const artistData = await profileService.getArtistDetails(userEntity.entity_id);

        if (artistData) {
          userProfile.artist_data = {
            artist_name: userEntity.entity_name,
            profile_image_url: artistData.profile_image_url,
            genre: artistData.genre,
            region: artistData.region
          };
        } else {
          userProfile.artist_data = {
            artist_name: userEntity.entity_name
          };
        }
      }
      else if (userEntity.entity_type === 'venue') {
        userProfile.user_type = 'venue';
        const venueData = await profileService.getVenueProfile(userEntity.entity_id);

        if (venueData) {
          userProfile.venue_data = {
            venue_name: userEntity.entity_name,
            venue_type: venueData.venue_type,
            address: venueData.address,
            logo_url: venueData.logo_url
          };
        } else {
          userProfile.venue_data = {
            venue_name: userEntity.entity_name
          };
        }
      }
      else if (userEntity.entity_type === 'agency') {
        userProfile.user_type = 'agency';
        
        try {
          const agencyData = await profileService.getAgencyProfile(userEntity.entity_id);

          if (agencyData) {
            userProfile.agency_data = {
              agency_name: userEntity.entity_name,
              profile_image_url: agencyData.profile_image_url,
              banner_image_url: agencyData.banner_image_url,
              company_name: agencyData.company_name,
              vat_number: agencyData.vat_number,
              invoice_address: agencyData.invoice_address
            };
          } else {
            // Create agency profile if it doesn't exist
            await profileService.createAgencyProfile(userEntity.entity_id, userEntity.entity_name);
            
            userProfile.agency_data = {
              agency_name: userEntity.entity_name
            };
          }
        } catch (err) {
          console.error('Error handling agency profile:', err);
          userProfile.agency_data = {
            agency_name: userEntity.entity_name
          };
        }
      }
    }

    return userProfile;
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return null;
  }
};

// Export the auth service
export const authService = {
  signOut,
  getSession,
  getUser,
  onAuthStateChange,
  fetchUserProfile
};
