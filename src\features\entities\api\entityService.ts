import { supabase } from '@/core/api/supabase';
import { EntityData } from '../types';

/**
 * Get an entity by ID
 * @param entityId The entity ID
 * @returns The entity
 */
export const getEntityById = async (entityId: string) => {
  try {
    const { data, error } = await supabase
      .from('entities')
      .select('*')
      .eq('id', entityId)
      .single();

    if (error) {
      console.error('Error fetching entity:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception in getEntityById:', error);
    return null;
  }
};

/**
 * Get a user's entity
 * @param userId The user ID
 * @returns The user's entity
 */
export const getUserEntity = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from('entity_users')
      .select(`
        entity_id,
        entity_type,
        is_primary,
        entities (
          id,
          name,
          description,
          entity_type
        )
      `)
      .eq('user_id', userId)
      .order('is_primary', { ascending: false });

    if (error) {
      console.error('Error fetching user entity:', error);
      return null;
    }

    if (!data || data.length === 0) {
      return null;
    }

    // Return the primary entity or the first one
    const primaryEntity = data.find(e => e.is_primary) || data[0];

    return {
      id: primaryEntity.entity_id,
      type: primaryEntity.entity_type,
      name: primaryEntity.entities.name,
      description: primaryEntity.entities.description,
      is_primary: primaryEntity.is_primary
    };
  } catch (error) {
    console.error('Exception in getUserEntity:', error);
    return null;
  }
};

/**
 * Get users for an entity
 * @param entityId The entity ID
 * @returns Array of users
 */
export const getEntityUsers = async (entityId: string) => {
  try {
    const { data, error } = await supabase
      .from('entity_users')
      .select(`
        user_id,
        is_primary,
        users (
          id,
          name,
          email,
          phone_number,
          profile_image_url
        )
      `)
      .eq('entity_id', entityId);

    if (error) {
      console.error('Error fetching entity users:', error);
      return null;
    }

    if (!data || data.length === 0) {
      return [];
    }

    // Transform the data to a more usable format
    return data.map(item => ({
      id: item.user_id,
      is_primary: item.is_primary,
      name: item.users.name,
      email: item.users.email,
      phone_number: item.users.phone_number,
      profile_image_url: item.users.profile_image_url
    }));
  } catch (error) {
    console.error('Exception in getEntityUsers:', error);
    return null;
  }
};

/**
 * Get artist details
 * @param entityId The entity ID
 * @returns The artist details
 */
export const getArtistDetails = async (entityId: string) => {
  try {
    // First try to get the artist details
    const { data: artistDetails, error: artistError } = await supabase
      .from('artist_profiles')
      .select('*')
      .eq('entity_id', entityId)
      .limit(1);

    if (artistError) {
      console.error('Error fetching artist details:', artistError);
      return null;
    }

    // Return the first item if it exists
    return artistDetails && artistDetails.length > 0 ? artistDetails[0] : null;
  } catch (error) {
    console.error('Exception in getArtistDetails:', error);
    return null;
  }
};

/**
 * Get venue profile
 * @param entityId The entity ID
 * @returns The venue profile
 */
export const getVenueProfile = async (entityId: string) => {
  try {
    // Get venue profile
    const { data: venueProfile, error: venueError } = await supabase
      .from('venue_profiles')
      .select('*')
      .eq('entity_id', entityId)
      .single();

    if (venueError) {
      console.error('Error fetching venue profile:', venueError);
      return null;
    }

    // Get entity details
    const { data: entityData, error: entityError } = await supabase
      .from('entities')
      .select('name, description')
      .eq('id', entityId)
      .single();

    if (entityError) {
      console.error('Error fetching entity details:', entityError);
      return null;
    }

    // Combine the data
    return {
      id: entityId,
      venue_name: entityData.name,
      description: entityData.description,
      venue_type: venueProfile.venue_type,
      address: venueProfile.address,
      logo_url: venueProfile.logo_url,
      banner_url: venueProfile.banner_url,
      invoice_address: venueProfile.invoice_address,
      company_name: venueProfile.company_name,
      vat_number: venueProfile.vat_number,
      updated_at: venueProfile.updated_at
    };
  } catch (error) {
    console.error('Exception in getVenueProfile:', error);
    return null;
  }
};

/**
 * Get agency profile
 * @param entityId The entity ID
 * @returns The agency profile
 */
export const getAgencyProfile = async (entityId: string) => {
  try {
    // Get agency profile
    const { data: agencyProfile, error: agencyError } = await supabase
      .from('agency_profiles')
      .select('*')
      .eq('entity_id', entityId)
      .single();

    if (agencyError) {
      console.error('Error fetching agency profile:', agencyError);
      return null;
    }

    // Get entity details
    const { data: entityData, error: entityError } = await supabase
      .from('entities')
      .select('name, description')
      .eq('id', entityId)
      .single();

    if (entityError) {
      console.error('Error fetching entity details:', entityError);
      return null;
    }

    // Combine the data
    return {
      id: entityId,
      agency_name: entityData.name,
      description: entityData.description,
      logo_url: agencyProfile.logo_url,
      banner_url: agencyProfile.banner_url,
      address: agencyProfile.address,
      invoice_address: agencyProfile.invoice_address,
      company_name: agencyProfile.company_name,
      vat_number: agencyProfile.vat_number,
      updated_at: agencyProfile.updated_at
    };
  } catch (error) {
    console.error('Exception in getAgencyProfile:', error);
    return null;
  }
};

/**
 * Fetch artists for booking selection
 * @returns Array of artists
 */
export const fetchArtists = async () => {
  try {
    // Get artists from the entity-based structure with their profiles
    // This ensures we only get artists that have proper profiles set up
    const { data: artistData, error: artistError } = await supabase
      .from('entities')
      .select(`
        id,
        name,
        artist_profiles!inner (
          entity_id,
          images,
          genre,
          region,
          is_placeholder,
          contact_email
        )
      `)
      .eq('entity_type', 'artist')
      .order('name', { ascending: true });

    if (artistError) {
      console.error('Error fetching artist entities:', artistError);
      throw artistError;
    }

    // Process entity data and ensure uniqueness by ID
    const artistMap = new Map();

    artistData.forEach(entity => {
      // Only add if not already in the map
      if (!artistMap.has(entity.id)) {
        // Get the first image from the images array if it exists
        const images = entity.artist_profiles?.images || [];
        const profileImage = images.length > 0 ? images[0] : null;

        artistMap.set(entity.id, {
          id: entity.id,
          artist_name: entity.name,
          name: entity.name,
          profile_image_url: profileImage, // Use the first image as profile image
          genre: entity.artist_profiles?.genre || null,
          region: entity.artist_profiles?.region || null,
          is_placeholder: entity.artist_profiles?.is_placeholder || false,
          contact_email: entity.artist_profiles?.contact_email || null
        });
      }
    });

    // Convert map to array
    const artists = Array.from(artistMap.values());

    return artists;
  } catch (error) {
    console.error('Error fetching artists:', error);
    throw error;
  }
};

/**
 * Create a new artist
 * @param artistName The name of the artist
 * @param contactEmail Optional email for placeholder artists
 * @param isPlaceholder Whether this is a placeholder artist without a user account
 * @returns The created artist object
 */
export const createArtist = async (artistName: string, contactEmail?: string, isPlaceholder: boolean = true) => {
  try {
    // Create a new entity for the artist
    const newEntityId = crypto.randomUUID();

    // 1. Create entity record
    const { error: entityError } = await supabase
      .from('entities')
      .insert({
        id: newEntityId,
        entity_type: 'artist',
        name: artistName,
        description: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (entityError) throw entityError;

    // 2. Create artist profile with new schema
    const { error: artistProfileError } = await supabase
      .from('artist_profiles')
      .insert({
        entity_id: newEntityId,
        genre: [],
        region: null,
        social_links: {},
        pricing_info: {},
        images: [],
        experiences: null,
        about: null,
        mixtapes: [],
        technical_rider: null,
        hospitality_rider: null,
        is_placeholder: isPlaceholder,
        contact_email: contactEmail || null,
        updated_at: new Date().toISOString()
      });

    if (artistProfileError) throw artistProfileError;

    return {
      id: newEntityId,
      artist_name: artistName,
      name: artistName,
      is_placeholder: isPlaceholder
    };
  } catch (error) {
    console.error('Error adding new artist:', error);
    throw error;
  }
};



/**
 * Get the number of bookings for an artist
 * @param artistId The artist entity ID
 * @returns The number of bookings
 */
export const getArtistBookingCount = async (artistId: string): Promise<number> => {
  try {
    if (!artistId) {
      console.warn('getArtistBookingCount called with empty artistId');
      return 0;
    }

    // Use the security definer function to bypass RLS
    try {
      const { data, error } = await supabase.rpc('get_artist_booking_count', {
        artist_id: artistId
      });

      if (error) {
        console.error('Error calling get_artist_booking_count:', error);

        // Fallback to direct count (which might be affected by RLS)
        try {
          const { data: countData, error: countError } = await supabase
            .from('bookings')
            .select('id')
            .eq('artist_id', artistId);

          if (countError) {
            console.error('Error in fallback count:', countError);
            return 0;
          }

          console.log(`Fallback count found ${countData?.length || 0} bookings for artist ${artistId}`);
          return countData?.length || 0;
        } catch (fallbackError) {
          console.error('Exception in fallback count:', fallbackError);
          return 0;
        }
      }

      // Log the result for debugging
      console.log(`Found ${data || 0} bookings for artist ${artistId} using RPC function`);

      return data || 0;
    } catch (countError) {
      console.error('Exception counting bookings:', countError);
      return 0;
    }
  } catch (error) {
    console.error('Exception in getArtistBookingCount:', error);
    return 0;
  }
};