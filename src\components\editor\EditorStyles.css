.ProseMirror .variable,
.ProseMirror variable {
  display: inline-flex;
  align-items: center;
  background-color: #dbeafe;
  color: #1e40af;
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid #bfdbfe;
  font-weight: 500;
  white-space: nowrap;
  cursor: default;
  user-select: all;
  position: relative;
  margin: 0 2px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  font-size: 0.95em;
  transition: all 0.2s ease;
}

.ProseMirror .variable:hover {
  background-color: #bfdbfe;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.ProseMirror .variable::before {
  content: "$";
  opacity: 0.5;
  font-size: 0.8em;
  margin-right: 2px;
}

.ProseMirror .variable::after {
  content: "}";
  opacity: 0.5;
  font-size: 0.8em;
  margin-left: 2px;
}

/* Base ProseMirror styles */
.ProseMirror {
  min-height: 100%;
  height: 100%;
  padding: 1rem;
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

.ProseMirror:focus {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
}

.ProseMirror p {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
  white-space: pre-wrap;
}

.ProseMirror p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

/* Reset color and font styles */
.ProseMirror [style*="color"] {
  color: var(--tw-prose-body);
}

.ProseMirror [style*="font-family"] {
  font-family: inherit;
}

.ProseMirror table {
  border-collapse: collapse;
  table-layout: fixed;
  width: 100%;
  margin: 0;
  overflow: hidden;
}

.ProseMirror td,
.ProseMirror th {
  position: relative;
  border: 1px solid #e5e7eb;
  padding: 0.5rem;
  vertical-align: top;
  white-space: pre-wrap;
}

.ProseMirror th {
  background-color: #f3f4f6;
  font-weight: bold;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
}

.ProseMirror a {
  color: #2563eb;
  text-decoration: underline;
}

/* List styles */
.ProseMirror ul,
.ProseMirror ol {
  padding-left: 2rem;
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.ProseMirror ul li,
.ProseMirror ol li {
  margin-bottom: 0.5rem;
  white-space: pre-wrap;
}

.ProseMirror ul {
  list-style-type: disc;
}

.ProseMirror ol {
  list-style-type: decimal;
}

/* Indentation */
.ProseMirror [data-indent="1"] {
  padding-left: 2rem;
}

.ProseMirror [data-indent="2"] {
  padding-left: 4rem;
}

.ProseMirror [data-indent="3"] {
  padding-left: 6rem;
}

/* Headings */
.ProseMirror h1 {
  font-size: 2rem;
  font-weight: 700;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

.ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
}

.ProseMirror h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}
