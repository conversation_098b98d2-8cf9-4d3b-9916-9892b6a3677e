import { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { supabase } from '@/core/api/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { Loader2, Calendar as CalendarIcon, Clock } from 'lucide-react';
import { format, addHours } from 'date-fns';
import { cn, useToast } from '@/core';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';

interface BookingEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  onBookingUpdated: () => void;
  booking: any; // The booking to edit
}

const BookingEditModal = ({ isOpen, onClose, onBookingUpdated, booking }: BookingEditModalProps) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [date, setDate] = useState<Date | undefined>(undefined);
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [location, setLocation] = useState('');
  const [status, setStatus] = useState('');
  const [price, setPrice] = useState('');
  const [pricingType, setPricingType] = useState('');
  const [notes, setNotes] = useState('');
  const [updating, setUpdating] = useState(false);
  const { toast } = useToast();
  const { profile } = useAuth();

  useEffect(() => {
    if (booking) {
      setTitle(booking.title || '');
      setDescription(booking.description || '');
      setDate(booking.booking_start ? new Date(booking.booking_start) : undefined);
      setStartTime(booking.booking_start ? format(new Date(booking.booking_start), 'HH:mm') : '');
      setEndTime(booking.booking_end ? format(new Date(booking.booking_end), 'HH:mm') : '');
      setLocation(booking.location || '');
      setStatus(booking.status || 'pending');
      setPrice(booking.price ? booking.price.toString() : '');
      setPricingType(booking.pricing_type || 'fixed');
      setNotes(booking.notes || '');
    }
  }, [booking]);

  const handleUpdate = async () => {
    if (!title) {
      toast({
        title: "Title required",
        description: "Please provide a title for the booking.",
        variant: "destructive",
      });
      return;
    }

    if (!date) {
      toast({
        title: "Date required",
        description: "Please select a date for the booking.",
        variant: "destructive",
      });
      return;
    }

    if (!startTime || !endTime) {
      toast({
        title: "Time required",
        description: "Please provide start and end times for the booking.",
        variant: "destructive",
      });
      return;
    }

    try {
      setUpdating(true);

      // Create Date objects for start and end times
      const [startHours, startMinutes] = startTime.split(':').map(Number);
      const [endHours, endMinutes] = endTime.split(':').map(Number);

      const startDate = new Date(date);
      startDate.setHours(startHours, startMinutes, 0, 0);

      const endDate = new Date(date);
      endDate.setHours(endHours, endMinutes, 0, 0);

      // Ensure end time is after start time
      if (endDate <= startDate) {
        toast({
          title: "Invalid time range",
          description: "End time must be after start time.",
          variant: "destructive",
        });
        setUpdating(false);
        return;
      }

      // Get the current booking to determine the owner entity
      const { data: currentBooking, error: fetchError } = await supabase
        .from('bookings')
        .select('owner_entity_id, venue_id, artist_id')
        .eq('id', booking.id)
        .single();

      if (fetchError) {
        console.error('Error fetching current booking:', fetchError);
        toast({
          title: "Update failed",
          description: "Failed to fetch current booking details. Please try again.",
          variant: "destructive",
        });
        return;
      }

      // Use the existing owner_entity_id if available, otherwise determine based on user type
      const ownerEntityId = currentBooking.owner_entity_id ||
        (profile?.entity?.entity_type === 'venue' ? currentBooking.venue_id : currentBooking.artist_id);

      // Prepare booking data
      const bookingData = {
        title,
        description,
        booking_start: startDate.toISOString(),
        booking_end: endDate.toISOString(),
        location,
        status,
        price: price ? parseFloat(price) : null,
        pricing_type: pricingType,
        notes,
        owner_entity_id: ownerEntityId // Set the owner entity ID
      };

      // Update booking in the database
      const { error } = await supabase
        .from('bookings')
        .update(bookingData)
        .eq('id', booking.id);

      if (error) {
        console.error('Error updating booking:', error);
        toast({
          title: "Update failed",
          description: "Failed to update the booking. Please try again.",
          variant: "destructive",
        });
        return;
      }

      toast({
        title: "Booking updated",
        description: "Your booking has been successfully updated.",
      });

      onBookingUpdated();
    } catch (err) {
      console.error('Exception in handleUpdate:', err);
      toast({
        title: "Update failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setUpdating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[550px]">
        <DialogHeader>
          <DialogTitle>Edit Booking</DialogTitle>
          <DialogDescription>
            Update the details of your booking.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="title" className="text-right">
              Title
            </Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="col-span-3"
              placeholder="Concert at Venue XYZ"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="description" className="text-right">
              Description
            </Label>
            <Textarea
              id="description"
              value={description || ''}
              onChange={(e) => setDescription(e.target.value)}
              className="col-span-3"
              placeholder="Details about the booking"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="date" className="text-right">
              Date
            </Label>
            <div className="col-span-3">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !date && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {date ? format(date, "PPP") : "Select date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={date}
                    onSelect={setDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="startTime" className="text-right">
              Start Time
            </Label>
            <Input
              id="startTime"
              type="time"
              value={startTime}
              onChange={(e) => setStartTime(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="endTime" className="text-right">
              End Time
            </Label>
            <Input
              id="endTime"
              type="time"
              value={endTime}
              onChange={(e) => setEndTime(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="location" className="text-right">
              Location
            </Label>
            <Input
              id="location"
              value={location || ''}
              onChange={(e) => setLocation(e.target.value)}
              className="col-span-3"
              placeholder="Venue address or location"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="status" className="text-right">
              Status
            </Label>
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="confirmed">Confirmed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="price" className="text-right">
              Price
            </Label>
            <Input
              id="price"
              type="number"
              value={price}
              onChange={(e) => setPrice(e.target.value)}
              className="col-span-3"
              placeholder="0.00"
              min="0"
              step="0.01"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="pricingType" className="text-right">
              Pricing Type
            </Label>
            <Select value={pricingType} onValueChange={setPricingType}>
              <SelectTrigger className="col-span-3">
                <SelectValue placeholder="Select pricing type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="fixed">Fixed</SelectItem>
                <SelectItem value="hourly">Hourly</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="notes" className="text-right">
              Notes
            </Label>
            <Textarea
              id="notes"
              value={notes || ''}
              onChange={(e) => setNotes(e.target.value)}
              className="col-span-3"
              placeholder="Additional notes"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={updating}>
            Cancel
          </Button>
          <Button onClick={handleUpdate} disabled={updating}>
            {updating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Updating...
              </>
            ) : (
              'Update Booking'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default BookingEditModal;
