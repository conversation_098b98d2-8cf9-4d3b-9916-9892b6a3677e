import { supabase } from '@/core/api/supabase';
import { DocumentData } from '../types';

/**
 * Get all documents for a user
 * @param userId The user ID
 * @param userType The user type ('artist', 'venue', or 'agency')
 * @returns Array of documents
 */
export const getUserDocuments = async (userId: string, userType: 'artist' | 'venue' | 'agency') => {
  try {
    // First, get the user's entity
    const { data: entityUsers, error: entityError } = await supabase
      .from('entity_users')
      .select('entity_id')
      .eq('user_id', userId)
      .eq('entity_type', userType);

    if (entityError) {
      console.error('Error fetching user entities:', entityError);
      return null;
    }

    if (!entityUsers || entityUsers.length === 0) {
      console.error('No entities found for user');
      return [];
    }

    // Get all entity IDs for this user
    const entityIds = entityUsers.map(eu => eu.entity_id);

    // Get documents where the user's entity is either the owner or related to the booking
    const { data, error } = await supabase
      .from('documents')
      .select(`
        id,
        title,
        document_type,
        booking_id,
        file_url,
        status,
        created_at,
        updated_at,
        description,
        signed_by_artist,
        signed_by_venue,
        created_by,
        is_signable,
        bookings (
          booking_start,
          venue_id,
          artist_id,
          title,
          owner_entity_id
        )
      `)
      .or(
        `owner_entity_id.in.(${entityIds.join(',')}),` +
        `bookings.artist_id.in.(${entityIds.join(',')}),` +
        `bookings.venue_id.in.(${entityIds.join(',')})`
      )
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching documents:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception in getUserDocuments:', error);
    return null;
  }
};

/**
 * Get a document by ID
 * @param documentId The document ID
 * @returns The document
 */
export const getDocumentById = async (documentId: string) => {
  try {
    const { data, error } = await supabase
      .from('documents')
      .select(`
        id,
        title,
        document_type,
        booking_id,
        file_url,
        content,
        status,
        created_at,
        updated_at,
        description,
        signed_by_artist,
        signed_by_venue,
        created_by,
        is_signable,
        share_id,
        bookings (
          booking_start,
          booking_end,
          venue_id,
          artist_id,
          title,
          location,
          price,
          pricing_type,
          owner_entity_id
        )
      `)
      .eq('id', documentId)
      .single();

    if (error) {
      console.error('Error fetching document:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception in getDocumentById:', error);
    return null;
  }
};

/**
 * Upload a document
 * @param file The file to upload
 * @param bookingId The booking ID
 * @param userId The user ID
 * @param title The document title
 * @param description The document description
 * @param documentType The document type
 * @param isSignable Whether the document is signable
 * @returns The uploaded document
 */
export const uploadDocument = async (
  file: File,
  bookingId: string,
  userId: string,
  title: string,
  description: string = '',
  documentType: string = 'pdf',
  isSignable: boolean = false
) => {
  try {
    // 1. Upload the file to storage
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}_${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
    const filePath = `${bookingId}/${fileName}`;

    const { error: uploadError } = await supabase.storage
      .from('documents')
      .upload(filePath, file);

    if (uploadError) {
      console.error('Error uploading file:', uploadError);
      return { error: uploadError };
    }

    // 2. Get the public URL for the file
    const { data: { publicUrl } } = supabase.storage
      .from('documents')
      .getPublicUrl(filePath);

    // 3. Get the booking to determine the owner entity
    const { data: bookingData, error: bookingError } = await supabase
      .from('bookings')
      .select('owner_entity_id, venue_id')
      .eq('id', bookingId)
      .single();

    if (bookingError) {
      console.error('Error fetching booking for document:', bookingError);
      return { error: bookingError };
    }

    // Use owner_entity_id if available, otherwise fall back to venue_id
    const ownerEntityId = bookingData.owner_entity_id || bookingData.venue_id;

    // 4. Create a document record
    // Set the status based on whether the document is signable
    const status = isSignable ? 'pending' : 'uploaded';

    const documentData: DocumentData = {
      title,
      description,
      document_type: documentType,
      file_url: publicUrl,
      file_type: fileExt,
      booking_id: bookingId,
      created_by: userId,
      owner_entity_id: ownerEntityId,
      status: status,
      is_signable: isSignable
    };

    const { data: document, error: documentError } = await supabase
      .from('documents')
      .insert(documentData)
      .select();

    if (documentError) {
      console.error('Error creating document record:', documentError);
      return { error: documentError };
    }

    return { document };
  } catch (error) {
    console.error('Exception in uploadDocument:', error);
    return { error };
  }
};

/**
 * Generate a document from a template
 * @param bookingId The booking ID
 * @param templateId The template ID
 * @param title The document title
 * @param description The document description
 * @param userId The user ID
 * @param formattedContent The formatted content
 * @param isSignable Whether the document is signable
 * @returns The generated document
 */
export const generateDocumentFromTemplate = async (
  bookingId: string,
  templateId: string,
  title: string,
  description: string,
  userId: string,
  formattedContent?: string,
  isSignable: boolean = false
) => {
  try {
    // 1. Get the booking to determine the owner entity
    const { data: bookingData, error: bookingError } = await supabase
      .from('bookings')
      .select('owner_entity_id, venue_id')
      .eq('id', bookingId)
      .single();

    if (bookingError) {
      console.error('Error fetching booking for document template:', bookingError);
      return { error: bookingError };
    }

    // Use owner_entity_id if available, otherwise fall back to venue_id
    const ownerEntityId = bookingData.owner_entity_id || bookingData.venue_id;

    // 2. If no formatted content is provided, get the template content
    let content = formattedContent;
    if (!content) {
      const { data: templateData, error: templateError } = await supabase
        .from('contract_templates')
        .select('content')
        .eq('id', templateId)
        .single();

      if (templateError) {
        console.error('Error fetching template:', templateError);
        return { error: templateError };
      }

      content = templateData.content;
    }

    // 3. Create the document
    const documentData: DocumentData = {
      booking_id: bookingId,
      title,
      document_type: 'contract',
      content,
      created_by: userId,
      owner_entity_id: ownerEntityId,
      status: isSignable ? 'pending' : 'generated',
      is_signable: isSignable,
      description
    };

    const { data: document, error: documentError } = await supabase
      .from('documents')
      .insert(documentData)
      .select();

    if (documentError) {
      console.error('Error creating document from template:', documentError);
      return { error: documentError };
    }

    return { document };
  } catch (error) {
    console.error('Exception in generateDocumentFromTemplate:', error);
    return { error };
  }
};

/**
 * Toggle document signature
 * @param documentId The document ID
 * @param userId The user ID
 * @param userType The user type ('artist' or 'venue')
 * @returns The updated document
 */
export const toggleDocumentSignature = async (
  documentId: string,
  userId: string,
  userType: 'artist' | 'venue'
) => {
  try {
    // Call the RPC function to toggle the signature
    const { data, error } = await supabase.rpc(
      'toggle_document_signature',
      {
        p_document_id: documentId,
        p_user_id: userId,
        p_user_type: userType
      }
    );

    if (error) {
      console.error('Error toggling signature:', error);
      return { error };
    }

    return { data };
  } catch (error) {
    console.error('Exception in toggleDocumentSignature:', error);
    return { error };
  }
};

/**
 * Generate a share ID for a document
 * @param documentId The document ID
 * @returns The share ID
 */
export const generateDocumentShareId = async (documentId: string) => {
  try {
    const shareId = crypto.randomUUID();

    const { data, error } = await supabase
      .from('documents')
      .update({ share_id: shareId })
      .eq('id', documentId)
      .select('share_id')
      .single();

    if (error) {
      console.error('Error generating share ID:', error);
      return { error };
    }

    return { shareId: data.share_id };
  } catch (error) {
    console.error('Exception in generateDocumentShareId:', error);
    return { error };
  }
};

/**
 * Get a document by share ID
 * @param shareId The share ID
 * @returns The document
 */
export const getDocumentByShareId = async (shareId: string) => {
  try {
    const { data, error } = await supabase
      .from('documents')
      .select(`
        id,
        title,
        document_type,
        booking_id,
        file_url,
        content,
        status,
        created_at,
        updated_at,
        description,
        signed_by_artist,
        signed_by_venue,
        created_by,
        is_signable,
        bookings (
          booking_start,
          booking_end,
          venue_id,
          artist_id,
          title,
          location,
          price,
          pricing_type
        )
      `)
      .eq('share_id', shareId)
      .single();

    if (error) {
      console.error('Error fetching document by share ID:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception in getDocumentByShareId:', error);
    return null;
  }
};
