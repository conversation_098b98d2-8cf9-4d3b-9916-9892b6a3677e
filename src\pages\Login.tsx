
import { Link, Navigate } from 'react-router-dom';
import AuthForm from '@/components/auth/AuthForm';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Info } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

const Login = () => {
  const { user, profile, loading } = useAuth();
  const [searchParams] = useSearchParams();
  const [authMessage, setAuthMessage] = useState<{type: 'info' | 'error', message: string} | null>(null);

  useEffect(() => {
    // Check URL parameters for auth related messages
    const error = searchParams.get('error');
    const registered = searchParams.get('registered');

    if (error) {
      setAuthMessage({
        type: 'error',
        message: decodeURIComponent(error)
      });
    } else if (registered === 'true') {
      setAuthMessage({
        type: 'info',
        message: 'Registration successful! Please check your email to confirm your account.'
      });
    }
  }, [searchParams]);

  // If user is authenticated, redirect to the appropriate dashboard
  if (!loading && user) {
    // Redirect based on user type
    if (profile?.user_type === 'artist') {
      // Store user type in local storage for fallback
      localStorage.setItem('user_type', 'artist');
      return <Navigate to="/artist/dashboard" replace />;
    } else if (profile?.user_type === 'venue') {
      // Store user type in local storage for fallback
      localStorage.setItem('user_type', 'venue');
      return <Navigate to="/venue/dashboard" replace />;
    }

    // Default fallback if profile is not loaded yet but user is authenticated
    const lastKnownUserType = localStorage.getItem('user_type');
    if (lastKnownUserType === 'artist') {
      return <Navigate to="/artist/dashboard" replace />;
    }
    return <Navigate to="/venue/dashboard" replace />;
  }

  return <div className="min-h-screen flex flex-col justify-center items-center bg-gray-50">
      <div className="w-full max-w-md px-4 sm:px-0">
        <Card className="shadow-md">
          <CardContent className="p-8">
            <div className="text-center mb-6">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">Log in</h1>
              <p className="text-sm text-gray-600 mb-6">Welcome back! Login to access your bookings.</p>
            </div>

            {authMessage && (
              <Alert
                variant={authMessage.type === 'error' ? 'destructive' : 'default'}
                className="mb-6"
              >
                {authMessage.type === 'error' ?
                  <AlertCircle className="h-4 w-4" /> :
                  <Info className="h-4 w-4" />
                }
                <AlertDescription>{authMessage.message}</AlertDescription>
              </Alert>
            )}

            <AuthForm type="login" />

            <div className="mt-6 text-center">
              <p className="text-sm text-gray-600">
                Don't have an account?{' '}
                <Link to="/register" className="font-medium text-stagecloud-black hover:underline">
                  Sign up
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>;
};
export default Login;
