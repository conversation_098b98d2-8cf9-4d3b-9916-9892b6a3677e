
import { useState, useEffect } from 'react';
import { format, isBefore, startOfDay, isEqual, parse, compareAsc, differenceInHours, differenceInMinutes } from 'date-fns';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar as CalendarIcon, Clock, User, Users, MapPin, Loader2, AlertCircle, Search, X, Euro } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";

import { cn, calculateTotalPrice, formatCurrencyEU, showSuccessToast, showErrorToast } from "@/core";
import { supabase } from '@/core/api/supabase';
import { getUserVenues } from '@/features/entities/api/venueHelpers';
import { getUserEntityIdForBooking } from '@/features/entities/api/profileService';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface Booking {
  id: string;
  title: string;
  venue_id: string;
  artist_id: string;
  booking_start: string;
  booking_end: string;
  description: string | null;
  pricing_type: 'fixed' | 'hourly';
  price: number;
  status: string;
  location: string;
  artist_name?: string;
  venue_name?: string;
}

interface EditBookingModalProps {
  open: boolean;
  onClose: () => void;
  booking: Booking | null;
  userType: 'venue' | 'artist';
  onBookingUpdated?: () => void;
}

interface Artist {
  id: string;
  artist_name?: string | null;
  name?: string | null;
}

const EditBookingModal = ({
  open,
  onClose,
  booking,
  userType,
  onBookingUpdated
}: EditBookingModalProps) => {
  const [title, setTitle] = useState('');
  const [location, setLocation] = useState('');
  const [description, setDescription] = useState('');
  const [bookingType, setBookingType] = useState<'fixed' | 'hourly'>('fixed');
  const [price, setPrice] = useState('');
  const [loading, setLoading] = useState(false);
  const [artists, setArtists] = useState<Artist[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedArtist, setSelectedArtist] = useState<Artist | null>(null);
  const [venueId, setVenueId] = useState<string | null>(null);
  const [eventDate, setEventDate] = useState<Date>(new Date());
  const [startTime, setStartTime] = useState('18:00');
  const [endTime, setEndTime] = useState('22:00');
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [validationErrors, setValidationErrors] = useState<{
    dates?: string;
  }>({});
  const [totalPrice, setTotalPrice] = useState<number | null>(null);

  useEffect(() => {
    if (open && booking) {
      // Format dates
      const startDate = new Date(booking.booking_start);
      const endDate = new Date(booking.booking_end);

      setTitle(booking.title);
      setLocation(booking.location);
      setDescription(booking.description || '');
      setBookingType(booking.pricing_type);
      setPrice(String(booking.price));
      setVenueId(booking.venue_id);
      setEventDate(startDate);
      setEndDate(endDate);
      setStartTime(format(startDate, 'HH:mm'));
      setEndTime(format(endDate, 'HH:mm'));

      fetchArtists();
      getCurrentVenue();
    }
  }, [open, booking]);

  useEffect(() => {
    if (selectedArtist === null && booking && artists.length > 0) {
      const bookingArtist = artists.find(artist => artist.id === booking.artist_id);
      if (bookingArtist) {
        setSelectedArtist(bookingArtist);
      }
    }
  }, [artists, booking, selectedArtist]);

  useEffect(() => {
    validateDates();
  }, [eventDate, endDate, startTime, endTime]);

  useEffect(() => {
    // Calculate total price when booking type, price, or dates/times change
    if (bookingType === 'hourly' && price && eventDate && endDate && startTime && endTime) {
      try {
        const start = formatTimeForDatabase(eventDate, startTime);
        const end = formatTimeForDatabase(endDate, endTime);
        const calculatedPrice = calculateTotalPrice(price, bookingType, start, end);
        setTotalPrice(calculatedPrice);
      } catch (error) {
        console.error('Error calculating total price:', error);
        setTotalPrice(null);
      }
    } else {
      setTotalPrice(null);
    }
  }, [bookingType, price, eventDate, endDate, startTime, endTime]);

  const validateDates = () => {
    const errors: {dates?: string} = {};

    if (isBefore(endDate, eventDate) && !isEqual(endDate, eventDate)) {
      errors.dates = "End date cannot be before start date";
    }

    // Removed past date validation to allow bookings in the past

    // Add validation for same-day time comparison
    if (isEqual(startOfDay(eventDate), startOfDay(endDate))) {
      const startDateTime = parse(startTime, 'HH:mm', new Date());
      const endDateTime = parse(endTime, 'HH:mm', new Date());

      if (compareAsc(startDateTime, endDateTime) > 0) {
        errors.dates = "End time cannot be before start time on the same day";
      }
    }

    setValidationErrors(errors);
  };

  const getCurrentVenue = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        showErrorToast("You must be logged in to edit a booking");
        return;
      }

      // For venue users, we need to check which venues they have access to
      if (userType === 'venue' && !venueId) {
        const userVenues = await getUserVenues(user.id);

        if (!userVenues) {
          console.error('Error fetching user venues');
          showErrorToast("Could not find your venues");
          return;
        }

        if (userVenues.length > 0) {
          const primaryVenue = userVenues.find((v: any) => v.is_primary) || userVenues[0];
          setVenueId(primaryVenue.venue_id);
        } else {
          showErrorToast("No venues found for this user");
        }
      }
    } catch (error) {
      console.error('Error getting current venue:', error);
      showErrorToast('Failed to get venue information');
    }
  };

  const fetchArtists = async () => {
    try {
      // Get artists from the entity-based structure with their profiles
      // This ensures we only get artists that have proper profiles set up
      const { data: artistData, error: artistError } = await supabase
        .from('entities')
        .select(`
          id,
          name,
          artist_profiles!inner (
            entity_id,
            profile_image_url,
            genre,
            region
          )
        `)
        .eq('entity_type', 'artist')
        .order('name', { ascending: true });

      if (artistError) {
        console.error('Error fetching artist entities:', artistError);
        throw artistError;
      }

      // Process entity data and ensure uniqueness by ID
      const artistMap = new Map();

      artistData.forEach(entity => {
        // Only add if not already in the map
        if (!artistMap.has(entity.id)) {
          artistMap.set(entity.id, {
            id: entity.id,
            artist_name: entity.name,
            name: entity.name,
            profile_image_url: entity.artist_profiles?.profile_image_url || null,
            genre: entity.artist_profiles?.genre || null,
            region: entity.artist_profiles?.region || null
          });
        }
      });

      // Convert map to array
      const artists = Array.from(artistMap.values());

      setArtists(artists);
    } catch (error) {
      console.error('Error fetching artists:', error);
      showErrorToast('Failed to load artists');
      setArtists([]);
    }
  };

  const formatTimeForDatabase = (date: Date, timeString: string) => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const newDate = new Date(date);
    newDate.setHours(hours, minutes, 0, 0);
    return newDate;
  };

  const validateBookingData = () => {
    if (validationErrors.dates) {
      showErrorToast(validationErrors.dates);
      return false;
    }

    if (!venueId) {
      showErrorToast("No venue selected");
      return false;
    }

    if (!selectedArtist) {
      showErrorToast("Please select an artist");
      return false;
    }

    if (!title || !location) {
      showErrorToast('Please fill in all required fields');
      return false;
    }

    if (!price || parseFloat(price) <= 0) {
      showErrorToast('Please enter a valid price');
      return false;
    }

    if (!eventDate || !startTime || !endTime) {
      showErrorToast('Please fill in all date and time fields');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!booking || !validateBookingData()) return;

    setLoading(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        showErrorToast("You must be logged in to update a booking");
        return;
      }

      // Get the user's entity ID for the booking
      const userEntityId = await getUserEntityIdForBooking(user.id, userType);

      if (!userEntityId && userType === 'venue') {
        showErrorToast("Could not determine your venue entity. Please contact support.");
        return;
      }

      const bookingStart = formatTimeForDatabase(eventDate, startTime);
      const bookingEnd = formatTimeForDatabase(endDate, endTime);

      // Determine the owner entity ID based on user type
      const ownerEntityId = userType === 'venue'
        ? (venueId || userEntityId)
        : selectedArtist.id; // If artist is editing the booking, they own it

      const { error } = await supabase
        .from('bookings')
        .update({
          title: title,
          venue_id: userType === 'venue' ? (venueId || userEntityId) : venueId,
          artist_id: selectedArtist.id,
          booking_start: bookingStart.toISOString(),
          booking_end: bookingEnd.toISOString(),
          description: description,
          pricing_type: bookingType,
          price: parseFloat(price),
          location: location,
          owner_entity_id: ownerEntityId // Set the owner entity ID
        })
        .eq('id', booking.id)
        .select();

      if (error) throw error;

      showSuccessToast('Booking updated successfully!');
      if (onBookingUpdated) onBookingUpdated();
      onClose();
    } catch (error) {
      console.error('Error updating booking:', error);
      showErrorToast('Failed to update booking');
    } finally {
      setLoading(false);
    }
  };

  const getArtistDisplayName = (artist: Artist) => {
    return artist.artist_name || artist.name || 'Unknown Artist';
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const filteredArtists = artists.filter(artist =>
    !searchQuery ||
    (artist.artist_name && artist.artist_name.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (artist.name && artist.name.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  if (!booking) return null;

  return (
    <Dialog open={open} onOpenChange={open => !open && onClose()}>
      <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Booking</DialogTitle>
          <DialogDescription>Update the details for this booking.</DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          <h3 className="text-lg font-semibold">General Information</h3>

          <div className="grid grid-cols-1 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="title" className="required">
                Event Title
              </Label>
              <Input
                id="title"
                value={title}
                onChange={e => setTitle(e.target.value)}
                placeholder="e.g., Jazz Night"
                className="col-span-3"
                required
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="location" className="required">
                Location
              </Label>
              <div className="relative">
                <MapPin className="h-4 w-4 absolute top-3 left-3 text-gray-500" />
                <Input
                  id="location"
                  value={location}
                  onChange={e => setLocation(e.target.value)}
                  placeholder="Enter venue location"
                  className="pl-9"
                  required
                />
              </div>
            </div>
          </div>

          <h3 className="text-lg font-semibold">Event Date & Time</h3>

          {validationErrors.dates && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{validationErrors.dates}</AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="startDate" className="required">
                Start Date
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="startDate"
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !eventDate && "text-muted-foreground",
                      validationErrors.dates && "border-red-500"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {eventDate ? format(eventDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={eventDate}
                    onSelect={(date) => date && setEventDate(date)}
                    initialFocus
                    className="pointer-events-auto"
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="endDate" className="required">
                End Date
              </Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    id="endDate"
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !endDate && "text-muted-foreground",
                      validationErrors.dates && "border-red-500"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={(date) => date && setEndDate(date)}
                    disabled={(date) => isBefore(date, eventDate)}
                    initialFocus
                    className="pointer-events-auto"
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="startTime" className="required">
                Start Time
              </Label>
              <div className="relative">
                <Clock className="h-4 w-4 absolute top-3 left-3 text-gray-500" />
                <Input
                  id="startTime"
                  type="time"
                  value={startTime}
                  onChange={(e) => setStartTime(e.target.value)}
                  className="pl-9"
                  required
                />
              </div>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="endTime" className="required">
                End Time
              </Label>
              <div className="relative">
                <Clock className="h-4 w-4 absolute top-3 left-3 text-gray-500" />
                <Input
                  id="endTime"
                  type="time"
                  value={endTime}
                  onChange={(e) => setEndTime(e.target.value)}
                  className="pl-9"
                  required
                />
              </div>
            </div>
          </div>

          <h3 className="text-lg font-semibold">Artist</h3>

          <div className="grid gap-2">
            <Label htmlFor="artist" className="required">
              Artist Selection
            </Label>

            <div className="space-y-2">
              <div className="flex gap-2">
                <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
                  <SheetTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-between relative"
                    >
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 flex-shrink-0 text-gray-500" />
                        {selectedArtist ? (
                          <span className="truncate">
                            {getArtistDisplayName(selectedArtist)}
                          </span>
                        ) : (
                          <span className="text-muted-foreground">Select an artist</span>
                        )}
                      </div>
                      {selectedArtist && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 absolute right-2"
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedArtist(null);
                          }}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      )}
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="right" className="w-[300px] sm:w-[400px]">
                    <SheetHeader>
                      <SheetTitle>Select an Artist</SheetTitle>
                      <SheetDescription>
                        Choose an artist for this booking
                      </SheetDescription>
                    </SheetHeader>
                    <div className="py-4">
                      <div className="relative mb-4">
                        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search artists..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-8"
                        />
                      </div>

                      <div className="space-y-2 max-h-[50vh] overflow-y-auto pr-1">
                        {filteredArtists.length > 0 ? (
                          filteredArtists.map((artist) => {
                            const displayName = getArtistDisplayName(artist);
                            return (
                              <Button
                                key={artist.id}
                                variant="outline"
                                className={cn(
                                  "w-full justify-start text-left h-auto py-3",
                                  selectedArtist?.id === artist.id && "border-primary bg-primary/10"
                                )}
                                onClick={() => {
                                  setSelectedArtist(artist);
                                  setIsSheetOpen(false);
                                  setSearchQuery('');
                                }}
                              >
                                <Avatar className="h-8 w-8 mr-3">
                                  <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${displayName}`} />
                                  <AvatarFallback>{displayName ? getInitials(displayName) : "??"}</AvatarFallback>
                                </Avatar>
                                <div className="flex flex-col">
                                  <span className="font-medium">{displayName}</span>
                                  {artist.artist_name && artist.name && artist.artist_name !== artist.name && (
                                    <span className="text-xs text-muted-foreground">{artist.name}</span>
                                  )}
                                </div>
                              </Button>
                            );
                          })
                        ) : (
                          <div className="p-4 text-center text-sm text-muted-foreground border rounded-md">
                            <Users className="h-10 w-10 mx-auto mb-2 text-gray-400" />
                            <p>No artists found</p>
                            <p className="mt-1 text-xs">Try a different search term</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </SheetContent>
                </Sheet>
              </div>
            </div>

            {selectedArtist && (
              <div className="flex items-center mt-2 p-2 bg-gray-50 rounded-md">
                <Avatar className="h-8 w-8 mr-2">
                  <AvatarFallback>{getInitials(getArtistDisplayName(selectedArtist))}</AvatarFallback>
                </Avatar>
                <span>{getArtistDisplayName(selectedArtist)}</span>
                <Badge variant="outline" className="ml-2">Selected</Badge>
              </div>
            )}
          </div>

          <h3 className="text-lg font-semibold">Financial Details</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="grid gap-2">
              <Label htmlFor="bookingType" className="required">
                Booking Type
              </Label>
              <Select value={bookingType} onValueChange={value => setBookingType(value as 'fixed' | 'hourly')}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fixed">Fixed Rate</SelectItem>
                  <SelectItem value="hourly">Hourly Rate</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="price" className="required">
                {bookingType === 'fixed' ? 'Fixed Price' : 'Hourly Rate'}
              </Label>
              <div className="relative">
                <Euro className="h-4 w-4 absolute top-3 left-3 text-gray-500" />
                <Input
                  id="price"
                  type="number"
                  value={price}
                  onChange={e => setPrice(e.target.value)}
                  placeholder="0.00"
                  className="pl-9"
                  min="0"
                  step="0.01"
                  required
                />
              </div>
            </div>
          </div>

          {bookingType === 'hourly' && price && totalPrice !== null && (
            <div className="bg-gray-50 p-3 rounded-md border border-gray-200">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <Clock className="h-4 w-4 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-600">Estimated Duration:</span>
                </div>
                <span className="font-medium">
                  {eventDate && endDate && startTime && endTime ? (
                    (() => {
                      const start = formatTimeForDatabase(eventDate, startTime);
                      const end = formatTimeForDatabase(endDate, endTime);
                      const hours = differenceInHours(end, start);
                      const remainingMinutes = differenceInMinutes(end, start) % 60;
                      return `${hours}h${remainingMinutes > 0 ? ` ${remainingMinutes}m` : ''}`;
                    })()
                  ) : (
                    'N/A'
                  )}
                </span>
              </div>
              <div className="flex justify-between items-center mt-2">
                <div className="flex items-center">
                  <Euro className="h-4 w-4 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-600">Total Amount:</span>
                </div>
                <span className="font-medium">{formatCurrencyEU(totalPrice)}</span>
              </div>
              <p className="text-xs text-gray-500 mt-2">Based on {formatCurrencyEU(price)}/hour</p>
            </div>
          )}

          <div className="grid gap-2">
            <Label htmlFor="description">
              Description
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={e => setDescription(e.target.value)}
              placeholder="Add any special requirements or notes here"
              rows={4}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={loading || !!validationErrors.dates}
            className="bg-stagecloud-purple hover:bg-stagecloud-purple/90 bg-zinc-950 hover:bg-zinc-800"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Updating...
              </>
            ) : (
              'Update Booking'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditBookingModal;
