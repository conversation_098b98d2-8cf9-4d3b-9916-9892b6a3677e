/**
 * Supabase REST API utility
 * 
 * This file provides utilities for making REST API calls to Supabase
 * using the centralized configuration.
 */

import { SUPABASE_URL, SUPABASE_ANON_KEY } from '@/core/config/supabase';
import { supabase } from './supabase';

/**
 * Options for REST API calls
 */
export interface RestApiOptions {
  /** The HTTP method to use */
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  /** The path to the endpoint, e.g. 'agency_profiles' */
  path: string;
  /** Query parameters as an object */
  queryParams?: Record<string, string>;
  /** The request body for POST, PUT, PATCH requests */
  body?: any;
  /** Whether to use the current user's session token for authentication */
  useAuth?: boolean;
  /** Custom headers to include */
  headers?: Record<string, string>;
}

/**
 * Make a REST API call to Supabase
 * @param options The options for the REST API call
 * @returns The response data
 */
export const supabaseRestApi = async <T = any>(options: RestApiOptions): Promise<T> => {
  const {
    method = 'GET',
    path,
    queryParams = {},
    body,
    useAuth = true,
    headers = {}
  } = options;

  // Build the URL with query parameters
  let url = `${SUPABASE_URL}/rest/v1/${path}`;
  
  // Add query parameters if any
  const queryString = Object.entries(queryParams)
    .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
    .join('&');
  
  if (queryString) {
    url += `?${queryString}`;
  }

  // Prepare headers
  const requestHeaders: Record<string, string> = {
    'Content-Type': 'application/json',
    'apikey': SUPABASE_ANON_KEY,
    ...headers
  };

  // Add authorization header if needed
  if (useAuth) {
    try {
      const { data: sessionData } = await supabase.auth.getSession();
      const accessToken = sessionData.session?.access_token;
      
      if (accessToken) {
        requestHeaders['Authorization'] = `Bearer ${accessToken}`;
      } else {
        // Fall back to anonymous key if no session
        requestHeaders['Authorization'] = `Bearer ${SUPABASE_ANON_KEY}`;
      }
    } catch (error) {
      console.error('Error getting session for REST API call:', error);
      // Fall back to anonymous key
      requestHeaders['Authorization'] = `Bearer ${SUPABASE_ANON_KEY}`;
    }
  } else {
    // Use anonymous key for non-authenticated requests
    requestHeaders['Authorization'] = `Bearer ${SUPABASE_ANON_KEY}`;
  }

  // Prepare fetch options
  const fetchOptions: RequestInit = {
    method,
    headers: requestHeaders
  };

  // Add body for non-GET requests
  if (method !== 'GET' && body !== undefined) {
    fetchOptions.body = JSON.stringify(body);
  }

  try {
    const response = await fetch(url, fetchOptions);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Supabase REST API error (${response.status}): ${errorText}`);
    }
    
    // For DELETE requests or when no content is expected
    if (response.status === 204 || response.headers.get('content-length') === '0') {
      return {} as T;
    }
    
    return await response.json() as T;
  } catch (error) {
    console.error('Supabase REST API call failed:', error);
    throw error;
  }
};

/**
 * Get a single item by ID
 * @param table The table name
 * @param id The item ID
 * @returns The item or null if not found
 */
export const getById = async <T = any>(table: string, id: string): Promise<T | null> => {
  try {
    const result = await supabaseRestApi<T[]>({
      path: table,
      queryParams: {
        id: `eq.${id}`,
        limit: '1'
      }
    });
    
    return result && result.length > 0 ? result[0] : null;
  } catch (error) {
    console.error(`Error getting ${table} by ID:`, error);
    return null;
  }
};

/**
 * Get items by a field value
 * @param table The table name
 * @param field The field name
 * @param value The field value
 * @returns Array of matching items
 */
export const getByField = async <T = any>(
  table: string, 
  field: string, 
  value: string | number | boolean
): Promise<T[]> => {
  try {
    return await supabaseRestApi<T[]>({
      path: table,
      queryParams: {
        [field]: `eq.${value}`
      }
    });
  } catch (error) {
    console.error(`Error getting ${table} by ${field}:`, error);
    return [];
  }
};

/**
 * Insert a new item
 * @param table The table name
 * @param data The data to insert
 * @returns The inserted item
 */
export const insert = async <T = any>(table: string, data: any): Promise<T | null> => {
  try {
    const result = await supabaseRestApi<T[]>({
      method: 'POST',
      path: table,
      body: data,
      headers: {
        'Prefer': 'return=representation'
      }
    });
    
    return result && result.length > 0 ? result[0] : null;
  } catch (error) {
    console.error(`Error inserting into ${table}:`, error);
    return null;
  }
};

/**
 * Update an item by ID
 * @param table The table name
 * @param id The item ID
 * @param data The data to update
 * @returns The updated item
 */
export const updateById = async <T = any>(table: string, id: string, data: any): Promise<T | null> => {
  try {
    const result = await supabaseRestApi<T[]>({
      method: 'PATCH',
      path: table,
      queryParams: {
        id: `eq.${id}`
      },
      body: data,
      headers: {
        'Prefer': 'return=representation'
      }
    });
    
    return result && result.length > 0 ? result[0] : null;
  } catch (error) {
    console.error(`Error updating ${table}:`, error);
    return null;
  }
};

/**
 * Delete an item by ID
 * @param table The table name
 * @param id The item ID
 * @returns Success status
 */
export const deleteById = async (table: string, id: string): Promise<boolean> => {
  try {
    await supabaseRestApi({
      method: 'DELETE',
      path: table,
      queryParams: {
        id: `eq.${id}`
      }
    });
    
    return true;
  } catch (error) {
    console.error(`Error deleting from ${table}:`, error);
    return false;
  }
};
