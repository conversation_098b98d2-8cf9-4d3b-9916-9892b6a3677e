
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Loader2 } from "lucide-react";

const Index = () => {
  const navigate = useNavigate();
  const { user, profile, loading } = useAuth();

  useEffect(() => {
    if (loading) return;

    ('Index page - User:', user?.id);
    ('Index page - Profile:', profile);

    // If user is authenticated, redirect to appropriate dashboard
    if (user) {
      // Check if profile is loaded and has a user_type
      if (profile) {
        ('Redirecting based on user type:', profile.user_type);

        if (profile.user_type === 'artist') {
          navigate('/artist/dashboard');
        } else if (profile.user_type === 'venue') {
          navigate('/venue/dashboard');
        } else {
          console.warn('Unknown user type:', profile.user_type);
          // Default to venue dashboard for unknown user types
          navigate('/venue/dashboard');
        }
      } else {
        console.warn('User authenticated but profile not loaded, using fallback');
        // If profile is not loaded yet, check local storage for last known user type
        const lastKnownUserType = localStorage.getItem('user_type');

        if (lastKnownUserType === 'artist') {
          navigate('/artist/dashboard');
        } else {
          // Default to venue dashboard
          navigate('/venue/dashboard');
        }
      }
    } else {
      // If not authenticated, redirect to login
      navigate('/login');
    }
  }, [navigate, user, profile, loading]);

  // Show loading indicator while auth state is being determined
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        <span className="ml-2 text-gray-500">Loading...</span>
      </div>
    );
  }

  return null;
};

export default Index;
