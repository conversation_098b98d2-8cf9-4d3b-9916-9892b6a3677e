
import React, { useState } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card } from '@/components/ui/card';
import { Calendar } from '@/components/ui/calendar';

const VenueCalendar = () => {
  const [date, setDate] = useState<Date | undefined>(new Date());

  return (
    <DashboardLayout userType="venue">
      <div className="space-y-6">
        <h1 className="text-3xl font-bold mb-6">Venue Calendar</h1>

        <Card className="p-4">
          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex-1">
              <h2 className="text-xl font-semibold mb-4">Bookings Calendar</h2>
              <div className="border rounded-md">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={setDate}
                  className="rounded-md"
                />
              </div>
            </div>

            <div className="flex-1 border-t pt-6 md:pt-0 md:border-t-0 md:border-l md:pl-6">
              <h2 className="text-xl font-semibold mb-4">
                {date ? date.toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                }) : 'Select a date'}
              </h2>
              <div className="space-y-2">
                <p className="text-gray-600">No events scheduled for this day</p>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default VenueCalendar;
