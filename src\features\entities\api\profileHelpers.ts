import { supabase } from '@/core/api/supabase';

/**
 * Get a user's entity
 */
export const getUserEntity = async (userId: string) => {
  try {
    // First try the view
    try {
      const { data, error } = await supabase
        .from('user_with_entities')
        .select('*')
        .eq('user_id', userId)
        .order('is_primary', { ascending: false })
        .limit(1);

      if (error) {
        console.error('Error fetching user entity from view:', error);
      } else if (data && data.length > 0) {
        // Store the entity type in localStorage for fallback
        if (data[0].entity_type) {
          localStorage.setItem('user_type', data[0].entity_type);
        }

        return data[0];
      }
    } catch (viewError) {
      // Check if this is a missing table error
      if (String(viewError).includes("relation \"public.profiles\" does not exist")) {
        console.log('Profiles table does not exist, skipping view query');
      } else {
        console.error('Exception querying user_with_entities view:', viewError);
      }
    }

    // If view doesn't work, try direct query to entity_users
    const { data: entityUserData, error: entityUserError } = await supabase
      .from('entity_users')
      .select('entity_id, role, is_primary')
      .eq('user_id', userId)
      .order('is_primary', { ascending: false })
      .limit(1);

    if (entityUserError) {
      console.error('Error fetching from entity_users:', entityUserError);
      return null;
    }

    if (!entityUserData || entityUserData.length === 0) {
      // Try to get user type from auth metadata as last resort
      try {
        const { data: authUser } = await supabase.auth.getUser(userId);
        if (authUser?.user?.user_metadata?.user_type) {
          const userType = authUser.user.user_metadata.user_type;
          const entityName = authUser.user.user_metadata.artist_name ||
                           authUser.user.user_metadata.venue_name ||
                           authUser.user.user_metadata.agency_name ||
                           authUser.user.user_metadata.name ||
                           'Unknown';

          // Store the user type in localStorage for fallback
          localStorage.setItem('user_type', userType);

          // Return a synthetic entity based on metadata
          return {
            user_id: userId,
            entity_id: null,
            entity_type: userType,
            entity_name: entityName,
            role: 'owner',
            is_primary: true
          };
        }
      } catch (authError) {
        console.error('Error fetching auth user metadata:', authError);
      }

      return null;
    }

    // Get entity details
    const entityId = entityUserData[0].entity_id;
    const { data: entityData, error: entityError } = await supabase
      .from('entities')
      .select('id, entity_type, name')
      .eq('id', entityId)
      .limit(1);

    if (entityError) {
      console.error('Error fetching entity details:', entityError);
      return null;
    }

    if (!entityData || entityData.length === 0) {
      return null;
    }

    // Construct entity data
    const result = {
      user_id: userId,
      entity_id: entityData[0].id,
      entity_type: entityData[0].entity_type,
      entity_name: entityData[0].name,
      role: entityUserData[0].role,
      is_primary: entityUserData[0].is_primary
    };

    // Store the entity type in localStorage for fallback
    localStorage.setItem('user_type', entityData[0].entity_type);

    return result;
  } catch (error) {
    console.error('Exception in getUserEntity:', error);
    return null;
  }
};

/**
 * Get a user profile by ID
 */
export const getProfileById = async (userId: string) => {
  try {
    // Get the user from the users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, name, email, phone_number, profile_image_url')
      .eq('id', userId)
      .limit(1);

    if (userError) {
      console.error(`Error fetching user profile: ${userError.message}`);
      return null;
    }

    if (!userData || userData.length === 0) {
      // Try to get user info from auth.users
      try {
        // First get the current session to ensure we have a valid token
        const { data: sessionData, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          console.error('Error getting session:', sessionError);
          return null;
        }

        if (!sessionData.session) {
          console.error('No active session found');
          return null;
        }

        // Now get the user with the valid session
        const { data: authUser, error: authError } = await supabase.auth.getUser();

        if (authError) {
          console.error('Error fetching auth user:', authError);
          return null;
        }

        if (authUser?.user) {
          console.log('Found auth user, creating profile record');

          // Create the user record
          const { data: newUser, error: insertError } = await supabase
            .from('users')
            .insert({
              id: userId,
              email: authUser.user.email,
              name: authUser.user.user_metadata?.name || 'User',
              phone_number: null,
              profile_image_url: null,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select()
            .single();

          if (insertError) {
            console.error(`Error creating user record: ${insertError.message}`);
            return null;
          }

          return newUser;
        } else {
          console.error('Auth user not found even with valid session');
          return null;
        }
      } catch (authError) {
        console.error('Exception in auth user fetch:', authError);
        return null;
      }

      return null;
    }

    return userData[0];
  } catch (error) {
    console.error('Exception in getProfileById:', error);
    return null;
  }
};

/**
 * Update user profile
 */
export const updateUserProfile = async (userId: string, profileData: any) => {
  try {
    // First check if the user exists
    const { data: existingUser, error: fetchError } = await supabase
      .from('users')
      .select('id, email')
      .eq('id', userId)
      .maybeSingle(); // Use maybeSingle instead of single to avoid error when no record is found

    if (fetchError && fetchError.code !== 'PGRST116') {
      console.error('Error checking user existence:', fetchError);
      return { error: fetchError };
    }

    let result: any;

    // If the user exists, update it while preserving the email
    if (existingUser) {
      // Only update the fields that are provided in profileData, preserving other fields
      result = await supabase
        .from('users')
        .update(profileData)
        .eq('id', userId)
        .select();
    } else {
      // If the user doesn't exist, create it with required fields
      const dataToInsert = {
        id: userId,
        ...profileData
      };

      result = await supabase
        .from('users')
        .insert(dataToInsert)
        .select();
    }

    if (result.error) {
      console.error('Error updating/creating user profile:', result.error);
      return { error: result.error };
    }

    return { data: result.data[0] };
  } catch (error) {
    console.error('Exception in updateUserProfile:', error);
    return { error };
  }
};

/**
 * Upload profile image
 */
export const uploadProfileImage = async (userId: string, file: File) => {
  try {
    // Create a unique file name
    const fileExt = file.name.split('.').pop();
    const fileName = `${userId}_${Date.now()}.${fileExt}`;
    const filePath = `profiles/${fileName}`;

    // Upload the file to Supabase storage
    const { error: uploadError } = await supabase.storage
      .from('profiles')
      .upload(filePath, file);

    if (uploadError) {
      console.error('Error uploading profile image:', uploadError);
      return { error: uploadError };
    }

    // Get the public URL for the file
    const { data: { publicUrl } } = supabase.storage
      .from('profiles')
      .getPublicUrl(filePath);

    // Update the user profile with the new image URL
    const { error: updateError } = await supabase
      .from('users')
      .update({ profile_image_url: publicUrl })
      .eq('id', userId);

    if (updateError) {
      console.error('Error updating user profile with image URL:', updateError);
      return { error: updateError };
    }

    return { data: { profile_image_url: publicUrl } };
  } catch (error) {
    console.error('Exception in uploadProfileImage:', error);
    return { error };
  }
};

/**
 * Update entity profile
 */
export const updateEntityProfile = async (entityId: string, entityType: 'artist' | 'venue' | 'agency', entityData: any, profileData: any) => {
  try {
    // Start a transaction to update both entity and profile
    const { error: entityError } = await supabase
      .from('entities')
      .update({
        name: entityData.name,
        description: entityData.description,
        updated_at: new Date().toISOString()
      })
      .eq('id', entityId);

    if (entityError) {
      console.error('Error updating entity:', entityError);
      return { error: entityError };
    }

    // Update profile based on entity type
    let profileError: any;
    if (entityType === 'artist') {
      const { error } = await supabase
        .from('artist_profiles')
        .update({
          ...profileData,
          updated_at: new Date().toISOString()
        })
        .eq('entity_id', entityId);
      profileError = error;
    } else if (entityType === 'venue') {
      const { error } = await supabase
        .from('venue_profiles')
        .update({
          ...profileData,
          updated_at: new Date().toISOString()
        })
        .eq('entity_id', entityId);
      profileError = error;
    } else if (entityType === 'agency') {
      // Use direct SQL for agency profiles since TypeScript doesn't know about this table yet
      try {
        // @ts-ignore - Ignore TypeScript errors for this operation
        const { error } = await supabase
          .from('agency_profiles')
          .update({
            ...profileData,
            updated_at: new Date().toISOString()
          })
          .eq('entity_id', entityId);
        profileError = error;
      } catch (err) {
        console.error('Error updating agency profile:', err);
        profileError = err;
      }
    }

    if (profileError) {
      console.error('Error updating profile:', profileError);
      return { error: profileError };
    }

    return { data: { entityId, entityType } };
  } catch (error) {
    console.error('Exception in updateEntityProfile:', error);
    return { error };
  }
};

/**
 * Get user profile for booking
 */
export const getUserProfileForBooking = async (userId: string) => {
  try {
    // First check if this is a user ID
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, name, email, phone_number')
      .eq('id', userId)
      .limit(1);

    if (!userError && userData && userData.length > 0) {
      // This is a user ID
      return userData[0];
    }

    // If not a user ID, check if it's an entity ID and get the primary user
    const primaryUser = await getEntityPrimaryUser(userId);
    if (primaryUser) {
      return {
        id: primaryUser.primary_user_id,
        name: primaryUser.primary_user_name,
        email: primaryUser.primary_user_email,
        phone_number: null // Not available in the view
      };
    }

    // Fallback: If we still can't find a user, check if this is an entity and get its name
    const { data: entityData, error: entityError } = await supabase
      .from('entities')
      .select('id, name, entity_type')
      .eq('id', userId)
      .limit(1);

    if (!entityError && entityData && entityData.length > 0) {
      // Create a synthetic user profile based on the entity
      return {
        id: userId,
        name: entityData[0].name,
        email: null,
        phone_number: null,
        entity_type: entityData[0].entity_type
      };
    }

    // Last resort fallback - return a placeholder user
    console.warn(`Could not find user or entity for ID: ${userId}, returning placeholder`);
    return {
      id: userId,
      name: 'Unknown User',
      email: null,
      phone_number: null
    };
  } catch (error) {
    console.error('Exception in getUserProfileForBooking:', error);
    // Return a placeholder rather than null to prevent cascading failures
    return {
      id: userId,
      name: 'Unknown User',
      email: null,
      phone_number: null
    };
  }
};

/**
 * Get entity primary user
 */
export const getEntityPrimaryUser = async (entityId: string) => {
  try {
    // First try using the view
    const { data, error } = await supabase
      .from('entity_with_primary_users')
      .select('*')
      .eq('entity_id', entityId)
      .limit(1);

    if (!error && data && data.length > 0) {
      return data[0];
    }

    if (error) {
      console.error('Error fetching entity primary user from view:', error);
    }

    // Fallback: If view doesn't work or returns no results, try direct query to entity_users
    console.log('Falling back to direct entity_users query for entity:', entityId);
    const { data: entityUsers, error: entityUsersError } = await supabase
      .from('entity_users')
      .select(`
        id,
        user_id,
        role,
        is_primary,
        users (
          id,
          name,
          email
        )
      `)
      .eq('entity_id', entityId)
      .eq('is_primary', true)
      .limit(1);

    if (entityUsersError) {
      console.error('Error fetching entity primary user from entity_users:', entityUsersError);
      return null;
    }

    if (entityUsers && entityUsers.length > 0 && entityUsers[0].users) {
      // Transform to match the expected format from the view
      return {
        entity_id: entityId,
        primary_user_id: entityUsers[0].user_id,
        primary_user_name: entityUsers[0].users.name,
        primary_user_email: entityUsers[0].users.email,
        primary_user_role: entityUsers[0].role
      };
    }

    // If still no primary user, try any user for this entity
    const { data: anyUsers, error: anyUsersError } = await supabase
      .from('entity_users')
      .select(`
        id,
        user_id,
        role,
        users (
          id,
          name,
          email
        )
      `)
      .eq('entity_id', entityId)
      .limit(1);

    if (anyUsersError) {
      console.error('Error fetching any entity user:', anyUsersError);
      return null;
    }

    if (anyUsers && anyUsers.length > 0 && anyUsers[0].users) {
      // Transform to match the expected format from the view
      return {
        entity_id: entityId,
        primary_user_id: anyUsers[0].user_id,
        primary_user_name: anyUsers[0].users.name,
        primary_user_email: anyUsers[0].users.email,
        primary_user_role: anyUsers[0].role
      };
    }

    return null;
  } catch (error) {
    console.error('Exception in getEntityPrimaryUser:', error);
    return null;
  }
};

/**
 * Get entity by ID
 */
export const getEntityById = async (entityId: string) => {
  try {
    const { data, error } = await supabase
      .from('entities')
      .select('*')
      .eq('id', entityId)
      .limit(1);

    if (error) {
      console.error('Error fetching entity:', error);
      return null;
    }

    return data && data.length > 0 ? data[0] : null;
  } catch (error) {
    console.error('Exception in getEntityById:', error);
    return null;
  }
};

/**
 * Get user entities
 */
export const getUserEntities = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from('user_with_entities')
      .select('*')
      .eq('user_id', userId)
      .order('is_primary', { ascending: false });

    if (error) {
      console.error('Error fetching user entities:', error);
      return null;
    }

    return data || [];
  } catch (error) {
    console.error('Exception in getUserEntities:', error);
    return null;
  }
};

/**
 * Get user primary entity ID
 */
export const getUserPrimaryEntityId = async (userId: string, entityType: string) => {
  try {
    const { data, error } = await supabase
      .from('user_with_entities')
      .select('entity_id')
      .eq('user_id', userId)
      .eq('entity_type', entityType)
      .eq('is_primary', true)
      .limit(1);

    if (error) {
      console.error(`Error fetching user's primary ${entityType} entity:`, error);
      return null;
    }

    if (data && data.length > 0) {
      return data[0].entity_id;
    }

    // If no primary entity found, try to get any entity of this type
    const { data: anyEntityData, error: anyEntityError } = await supabase
      .from('user_with_entities')
      .select('entity_id')
      .eq('user_id', userId)
      .eq('entity_type', entityType)
      .limit(1);

    if (anyEntityError) {
      console.error(`Error fetching any ${entityType} entity:`, anyEntityError);
      return null;
    }

    if (anyEntityData && anyEntityData.length > 0) {
      return anyEntityData[0].entity_id;
    }

    return null;
  } catch (error) {
    console.error(`Exception in getUserPrimaryEntityId for ${entityType}:`, error);
    return null;
  }
};

/**
 * Get entity users
 */
export const getEntityUsers = async (entityId: string) => {
  try {
    const { data, error } = await supabase
      .from('user_with_entities')
      .select('*')
      .eq('entity_id', entityId);

    if (error) {
      console.error('Error fetching entity users:', error);
      return null;
    }

    return data || [];
  } catch (error) {
    console.error('Exception in getEntityUsers:', error);
    return null;
  }
};
