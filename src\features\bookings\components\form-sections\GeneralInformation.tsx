import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { MapPin } from 'lucide-react';

interface GeneralInformationProps {
  title: string;
  setTitle: (title: string) => void;
  location: string;
  setLocation: (location: string) => void;
  description: string;
  setDescription: (description: string) => void;
}

const GeneralInformation = ({
  title,
  setTitle,
  location,
  setLocation,
  description,
  setDescription
}: GeneralInformationProps) => {
  return (
    <>
      <h3 className="text-lg font-semibold">General Information</h3>

      <div className="grid grid-cols-1 gap-4">
        <div className="grid gap-2">
          <Label htmlFor="title" className="required">
            Event Title
          </Label>
          <Input
            id="title"
            value={title}
            onChange={e => setTitle(e.target.value)}
            placeholder="e.g., Jazz Night"
            className="col-span-3"
            required
          />
        </div>

        <div className="grid gap-2">
          <Label htmlFor="location" className="required">
            Location
          </Label>
          <div className="relative">
            <MapPin className="h-4 w-4 absolute top-3 left-3 text-gray-500" />
            <Input
              id="location"
              value={location}
              onChange={e => setLocation(e.target.value)}
              placeholder="Enter venue location"
              className="pl-9"
              required
            />
          </div>
        </div>
      </div>

      <div className="grid gap-2">
        <Label htmlFor="description">
          Description
        </Label>
        <Textarea
          id="description"
          value={description}
          onChange={e => setDescription(e.target.value)}
          placeholder="Add any special requirements or notes here"
          rows={4}
        />
      </div>
    </>
  );
};

export default GeneralInformation;
