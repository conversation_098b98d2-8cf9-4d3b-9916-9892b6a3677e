import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { toast } from 'sonner';
import { Music, Building2, Briefcase } from 'lucide-react';
import { supabase } from '@/core/api/supabase';
import { supabaseRestApi } from '@/core/api/supabase-rest';

import { getUserEntity } from '@/features/entities/api/profileHelpers';
import { z } from 'zod';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useToast } from '@/core';
import { User } from '@supabase/supabase-js';

// Helper function to ensure user setup is complete
const ensureUserSetup = async (user: User) => {
  console.log('Ensuring user setup client-side for:', user.id);
  console.log('User metadata:', user.user_metadata);

  // Store the user type in localStorage for fallback
  if (user.user_metadata?.user_type) {
    const userType = user.user_metadata.user_type as string;
    console.log('Storing user type in localStorage from metadata:', userType);
    localStorage.setItem('user_type', userType);
  }

  // Check if this is an invitation registration
  const isInvitation = user.user_metadata?.is_invitation === true;
  const invitationEntityId = user.user_metadata?.invitation_entity_id as string | undefined;
  const invitationEntityType = user.user_metadata?.invitation_entity_type as string | undefined;

  if (isInvitation && invitationEntityId) {
    console.log('This is an invitation registration. Entity creation will be skipped.');
    console.log('User will be linked to existing entity:', invitationEntityId);
    console.log('Entity type:', invitationEntityType);

    // For invitation registrations, we don't need to create a new entity
    // The entity_user relationship will be created by the acceptInvitation function
    return;
  }

  try {
    // Check if user exists in public.users
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('id', user.id)
      .maybeSingle();

    if (userError && userError.code !== 'PGRST116') { // PGRST116 is 'no rows returned'
      console.error('Error checking user existence:', userError);
      return;
    }

    // If user doesn't exist, create it
    if (!userData) {
      console.log('User not found in public.users, creating...');
      const { error: insertError } = await supabase
        .from('users')
        .insert({
          id: user.id,
          email: user.email,
          name: user.user_metadata?.name || 'User',
          phone_number: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });

      if (insertError) {
        console.error('Error creating user record:', insertError);
        return;
      }

      console.log('User record created successfully');
    } else {
      console.log('User already exists in public.users');
    }

    // Check if user has an entity relationship
    const { data: entityUserData, error: entityUserError } = await supabase
      .from('entity_users')
      .select(`
        entity_id,
        entities (
          id,
          entity_type,
          name
        )
      `)
      .eq('user_id', user.id);

    if (entityUserError) {
      console.error('Error checking entity relationship:', entityUserError);
      return;
    }

    // If user already has entity relationships, log them and return
    if (entityUserData && entityUserData.length > 0) {
      console.log(`User already has ${entityUserData.length} entity relationships:`);
      entityUserData.forEach(relation => {
        console.log(`- Entity: ${relation.entities.name} (${relation.entity_id}), Type: ${relation.entities.entity_type}`);
      });

      // Check if user has an entity of the type specified in metadata
      if (user.user_metadata?.user_type) {
        const userType = user.user_metadata.user_type as string;
        const hasMatchingEntityType = entityUserData.some(
          relation => relation.entities.entity_type === userType
        );

        if (hasMatchingEntityType) {
          console.log(`User already has an entity of type ${userType}, no need to create a new one`);
          return;
        } else {
          console.log(`User has entities but none of type ${userType}, will create one`);
        }
      } else {
        // If no user_type in metadata, we'll use the first entity's type
        return;
      }
    }

    // If no entity relationship exists and user has a user_type in metadata, create one
    if ((!entityUserData || entityUserData.length === 0) && user.user_metadata?.user_type) {
      const userType = user.user_metadata.user_type as string;
      console.log('No entity relationship found, creating based on user type:', userType);

      if (userType === 'venue' || userType === 'artist' || userType === 'agency') {
        // Generate a UUID for the entity
        const entityId = crypto.randomUUID();
        console.log(`Generated new entity ID: ${entityId}`);

        // Create entity
        const { error: entityError } = await supabase
          .from('entities')
          .insert({
            id: entityId,
            entity_type: userType,
            name: userType === 'venue'
              ? (user.user_metadata.venue_name as string || user.user_metadata.name as string || 'Unnamed Venue')
              : userType === 'artist'
                ? (user.user_metadata.artist_name as string || user.user_metadata.name as string || 'Unnamed Artist')
                : (user.user_metadata.agency_name as string || user.user_metadata.name as string || 'Unnamed Agency'),
            description: null,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (entityError) {
          console.error('Error creating entity:', entityError);
          return;
        }

        console.log(`Entity created successfully with ID: ${entityId}`);

        // Create entity profile
        if (userType === 'venue') {
          const { error: profileError } = await supabase
            .from('venue_profiles')
            .insert({
              entity_id: entityId,
              venue_type: null,
              address: null,
              logo_url: null,
              banner_url: null,
              invoice_address: null,
              company_name: null,
              vat_number: null,
              public_contact_info: {},
              updated_at: new Date().toISOString()
            });

          if (profileError) {
            console.error('Error creating venue profile:', profileError);
            return;
          }

          console.log('Venue profile created successfully');
        } else if (userType === 'artist') {
          console.log('Creating artist profile for entity:', entityId);
          console.log('Artist name from metadata:', user.user_metadata?.artist_name);

          try {
            // Create the artist profile with minimal information
            const { data: profileData, error: profileError } = await supabase
              .from('artist_profiles')
              .insert({
                entity_id: entityId,
                genre: [],
                region: null,
                social_links: {},
                pricing_info: {},
                images: [],
                experiences: null,
                about: null,
                mixtapes: [],
                technical_rider: null,
                hospitality_rider: null,
                contact_email: user.email,
                updated_at: new Date().toISOString()
              })
              .select();

            if (profileError) {
              console.error('Error creating artist profile:', profileError);
              console.error('Error details:', JSON.stringify(profileError));
              return;
            }

            console.log('Artist profile created successfully:', profileData);
          } catch (err) {
            console.error('Exception creating artist profile:', err);
            return;
          }
        } else if (userType === 'agency') {
          // Create the agency profile using REST API
          console.log('Creating agency profile for entity:', entityId);
          console.log('User metadata for agency profile creation:', user.user_metadata);

          try {
            // Use our REST API utility
            // First, check if the agency profile already exists
            const existingProfiles = await supabaseRestApi({
              method: 'GET',
              path: 'agency_profiles',
              queryParams: {
                'entity_id': `eq.${entityId}`
              }
            });

            if (existingProfiles && existingProfiles.length > 0) {
              console.log('Agency profile already exists for entity:', entityId);
            } else {
              // Create the agency profile
              const agencyName = user.user_metadata?.agency_name as string || 'Unnamed Agency';
              console.log('Using agency name for profile:', agencyName);

              try {
                // Create the agency profile using our REST API utility
                await supabaseRestApi({
                  method: 'POST',
                  path: 'agency_profiles',
                  body: {
                    entity_id: entityId,
                    profile_image_url: null,
                    banner_image_url: null,
                    company_name: agencyName,
                    vat_number: null,
                    invoice_address: null,
                    social_links: {},
                    updated_at: new Date().toISOString()
                  },
                  headers: {
                    'Prefer': 'return=minimal'
                  }
                });

                console.log('Agency profile created successfully with REST API for entity:', entityId);
              } catch (apiError) {
                console.error('Error creating agency profile:', apiError);
                return;
              }
            }
          } catch (err) {
            console.error('Exception creating agency profile:', err);
            return;
          }
        }

        // Create entity-user relationship
        console.log('Creating entity-user relationship for entity:', entityId, 'and user:', user.id);

        // Determine if this should be the primary entity
        // If user has no other entities, this should be primary
        // If user has other entities but none of the same type, this should be primary for this type
        let isPrimary = true;
        if (entityUserData && entityUserData.length > 0) {
          // Check if user has other entities of the same type
          const hasEntitiesOfSameType = entityUserData.some(
            relation => relation.entities.entity_type === userType
          );

          // If user has other entities of the same type, this should not be primary
          isPrimary = !hasEntitiesOfSameType;
        }

        console.log(`Setting new entity as primary: ${isPrimary}`);

        try {
          const { data: relationshipData, error: relationshipError } = await supabase
            .from('entity_users')
            .insert({
              id: crypto.randomUUID(),
              user_id: user.id,
              entity_id: entityId,
              role: 'owner',
              is_primary: isPrimary,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            })
            .select();

          if (relationshipError) {
            console.error('Error creating entity-user relationship:', relationshipError);
            return;
          }

          console.log('Entity-user relationship created successfully:', relationshipData);
        } catch (err) {
          console.error('Exception creating entity-user relationship:', err);
          return;
        }

        console.log('Entity setup completed successfully');
      }
    } else if (entityUserData && entityUserData.length > 0) {
      console.log('User already has entity relationships, no need to create a new one');
    }
  } catch (error) {
    console.error('Exception in ensureUserSetup:', error);
  }
};

interface AuthFormProps {
  type: 'login' | 'register';
}

const loginSchema = z.object({
  email: z.string().email({
    message: 'Please enter a valid email address'
  }),
  password: z.string().min(6, {
    message: 'Password must be at least 6 characters'
  })
});
// Define the register schema
const registerSchema = z.object({
  name: z.string().min(2, {
    message: "Name is required"
  }),
  email: z.string().email({
    message: "Please enter a valid email address"
  }),
  password: z.string().min(6, {
    message: "Password must be at least 6 characters"
  }),
  confirmPassword: z.string(),
  userType: z.enum(['artist', 'venue', 'agency']),
  artistName: z.string().optional(),
  venueName: z.string().optional(),
  agencyName: z.string().optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ['confirmPassword']
}).refine((data) => {
  if (data.userType === 'artist') {
    return !!data.artistName;
  }
  return true;
}, {
  message: "Artist name is required for artist accounts",
  path: ['artistName']
}).refine((data) => {
  if (data.userType === 'venue') {
    return !!data.venueName;
  }
  return true;
}, {
  message: "Venue name is required for venue accounts",
  path: ['venueName']
}).refine((data) => {
  if (data.userType === 'agency') {
    return !!data.agencyName;
  }
  return true;
}, {
  message: "Agency name is required for agency accounts",
  path: ['agencyName']
});
type LoginFormValues = z.infer<typeof loginSchema>;
type RegisterFormValues = z.infer<typeof registerSchema>;

const AuthForm = ({ type }: AuthFormProps) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const { toast: uiToast } = useToast();

  const loginForm = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      email: '',
      password: ''
    }
  });

  const registerForm = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
      userType: 'venue',
      artistName: '',
      venueName: '',
      agencyName: ''
    }
  });

  const userType = registerForm.watch('userType');

  const handleLogin = async (values: LoginFormValues) => {
    setLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email: values.email,
        password: values.password
      });

      if (error) {
        switch (error.message) {
          case "Email not confirmed":
            uiToast({
              title: "Login failed",
              description: "Please check your email to confirm your account first.",
              variant: "destructive"
            });
            break;
          case "Invalid login credentials":
            uiToast({
              title: "Login failed",
              description: "The email or password you entered is incorrect. Please try again.",
              variant: "destructive"
            });
            break;
          default:
            uiToast({
              title: "Login failed",
              description: error.message || "An error occurred during login. Please try again.",
              variant: "destructive"
            });
        }

        setLoading(false);
        return;
      }

      if (data && data.user) {
        toast.success('Login successful!');

        // Ensure user setup is complete
        try {
          console.log('Ensuring user setup for:', data.user.id);
          await ensureUserSetup(data.user);
        } catch (setupError) {
          console.error('Exception ensuring user setup:', setupError);
        }

        // Get user entity to determine user type
        console.log('Getting user entity to determine user type');
        const userEntityData = await getUserEntity(data.user.id);
        console.log('User entity data:', userEntityData);

        let userType = 'venue';

        if (userEntityData && 'entity_type' in userEntityData) {
          userType = userEntityData.entity_type;
          console.log('Using entity type from database:', userType);
        } else if (data.user.user_metadata?.user_type) {
          userType = data.user.user_metadata.user_type as string;
          console.log('No entity found, using user metadata for user type:', userType);
        }

        console.log('Final user type for redirection:', userType);

        // Store user type in local storage for fallback
        localStorage.setItem('user_type', userType);
        console.log('Stored user type in localStorage:', userType);

        console.log('Redirecting after login based on user type:', userType);

        if (userType === 'artist') {
          console.log('Redirecting to artist dashboard after login');
          navigate('/artist/dashboard');
        } else if (userType === 'venue') {
          console.log('Redirecting to venue dashboard after login');
          navigate('/venue/dashboard');
        } else if (userType === 'agency') {
          console.log('Redirecting to agency dashboard after login');
          navigate('/agency/dashboard');
        } else {
          // Default fallback
          console.log('Redirecting to default venue dashboard after login');
          navigate('/venue/dashboard');
        }
      }
    } catch (error: any) {
      console.error('Login error:', error);
      uiToast({
        title: "Login failed",
        description: "An unexpected error occurred. Please try again later.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleRegister = async (values: RegisterFormValues) => {
    setLoading(true);
    try {
      console.log('Registration payload:', {
        email: values.email,
        password: values.password,
        metadata: {
          name: values.name,
          user_type: values.userType,
          artist_name: values.userType === 'artist' ? values.artistName : null,
          venue_name: values.userType === 'venue' ? values.venueName : null,
          agency_name: values.userType === 'agency' ? values.agencyName : null
        }
      });

      const { data, error } = await supabase.auth.signUp({
        email: values.email,
        password: values.password,
        options: {
          data: {
            name: values.name,
            user_type: values.userType,
            artist_name: values.userType === 'artist' ? values.artistName : null,
            venue_name: values.userType === 'venue' ? values.venueName : null,
            agency_name: values.userType === 'agency' ? values.agencyName : null
          }
        }
      });

      console.log('Supabase registration response:', { data, error });

      if (error) {
        console.error('Registration error details:', error);
        switch (true) {
          case error.message.includes("User already registered"):
            uiToast({
              title: "Registration failed",
              description: "This email is already registered. Please try logging in instead.",
              variant: "destructive"
            });
            break;
          case error.message.includes("Password should be at least 6 characters"):
            uiToast({
              title: "Registration failed",
              description: "Password must be at least 6 characters long.",
              variant: "destructive"
            });
            break;
          case error.message.includes("Database error saving new user"):
            console.error('Database error during registration. This might be due to a trigger function issue.');
            uiToast({
              title: "Registration failed",
              description: "We've updated the system. Please try registering again.",
              variant: "destructive"
            });
            break;
          default:
            uiToast({
              title: "Registration failed",
              description: error.message || "An error occurred during registration. Please try again.",
              variant: "destructive"
            });
        }
        setLoading(false);
        return;
      }

      if (!data.user?.id) {
        console.error('User created but no ID returned:', data);
        uiToast({
          title: "Registration failed",
          description: "We couldn't complete your registration. Please try again.",
          variant: "destructive"
        });
        setLoading(false);
        return;
      }

      try {
        // User record creation is now handled by the database trigger
        // We don't need to manually create the user record anymore
        console.log('User registration successful, proceeding with setup...');

        // For all user types, we'll manually ensure the entity is created
        if (data.user) {
          console.log(`${values.userType} user detected, ensuring entity setup is complete`);

          // Store the entity name in user metadata for agency users
          if (values.userType === 'agency' && values.agencyName) {
            console.log('Storing agency name in user metadata:', values.agencyName);

            const { error: updateError } = await supabase.auth.updateUser({
              data: {
                agency_name: values.agencyName
              }
            });

            if (updateError) {
              console.error('Error updating user metadata with agency name:', updateError);
            } else {
              console.log('User metadata updated with agency name');
            }
          }

          // Always create a new entity for the user
          await ensureUserSetup(data.user);
        } else {
          console.log('No user data returned from registration');
        }
      } catch (profileSetupError) {
        console.error('Exception during profile setup:', profileSetupError);
        uiToast({
          title: "Account creation issue",
          description: "Your account was created but we encountered problems setting up your profile.",
          variant: "default"
        });
      }

      // Store user type in local storage for fallback
      localStorage.setItem('user_type', values.userType);
      console.log('Stored user type in localStorage during registration:', values.userType);

      if (data.user && !data.session) {
        uiToast({
          title: "Registration successful",
          description: "Please check your email to confirm your account before logging in.",
          variant: "default"
        });
        setTimeout(() => {
          navigate('/login');
        }, 2000);
      } else if (data.user && data.session) {
        uiToast({
          title: "Registration successful",
          description: "Your account has been created and you're now logged in.",
          variant: "default"
        });

        // Get the actual user type from the entity
        console.log('Getting user entity to confirm user type before redirection');
        const userEntityData = await getUserEntity(data.user.id);
        console.log('User entity data for redirection:', userEntityData);

        // Determine the final user type for redirection
        let finalUserType = values.userType;

        if (userEntityData && 'entity_type' in userEntityData) {
          finalUserType = userEntityData.entity_type;
          console.log('Using entity type from database for redirection:', finalUserType);
        } else {
          console.log('Using registration form user type for redirection:', finalUserType);
        }

        console.log('Final user type for redirection:', finalUserType);

        setTimeout(() => {
          if (finalUserType === 'artist') {
            console.log('Redirecting to artist dashboard');
            navigate('/artist/dashboard');
          } else if (finalUserType === 'venue') {
            console.log('Redirecting to venue dashboard');
            navigate('/venue/dashboard');
          } else if (finalUserType === 'agency') {
            console.log('Redirecting to agency dashboard');
            navigate('/agency/dashboard');
          } else {
            // Default fallback
            console.log('Redirecting to default venue dashboard');
            navigate('/venue/dashboard');
          }
        }, 500);
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      uiToast({
        title: "Registration failed",
        description: "An unexpected error occurred. Please try again later.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };



  return <div className="">

      {type === 'login' ? <Form {...loginForm}>
          <form onSubmit={loginForm.handleSubmit(handleLogin)} className="space-y-6">
            <FormField control={loginForm.control} name="email" render={({
          field
        }) => <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input type="email" placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>} />

            <FormField control={loginForm.control} name="password" render={({
          field
        }) => <FormItem>
                  <div className="flex justify-between items-center">
                    <FormLabel>Password</FormLabel>
                    <Link to="/forgot-password" className="text-sm font-medium text-stagecloud-purple hover:text-stagecloud-purple/80">
                      Forgot password?
                    </Link>
                  </div>
                  <FormControl>
                    <Input type="password" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>} />

            <Button type="submit" disabled={loading} className="w-full bg-stagecloud-purple hover:bg-stagecloud-purple/90 text-white bg-zinc-950 hover:bg-zinc-800">
              {loading ? <div className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Signing in...
                </div> : 'Sign in'}
            </Button>
          </form>
        </Form> : <Form {...registerForm}>
          <form onSubmit={registerForm.handleSubmit(handleRegister)} className="space-y-6">
            <FormField control={registerForm.control} name="userType" render={({
          field
        }) => <FormItem className="space-y-4">
                  <FormLabel>I am a:</FormLabel>
                  <FormControl>
                    <RadioGroup className="flex flex-wrap gap-4" value={field.value} onValueChange={field.onChange}>
                      <div className="flex items-center space-x-2 border rounded-lg p-4 cursor-pointer hover:border-stagecloud-purple transition-colors flex-1">
                        <RadioGroupItem value="venue" id="venue" />
                        <Label htmlFor="venue" className="cursor-pointer flex items-center gap-2 w-full">
                          <Building2 className="h-5 w-5" />
                          <span>Venue</span>
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2 border rounded-lg p-4 cursor-pointer hover:border-stagecloud-purple transition-colors flex-1">
                        <RadioGroupItem value="artist" id="artist" />
                        <Label htmlFor="artist" className="cursor-pointer flex items-center gap-2 w-full">
                          <Music className="h-5 w-5" />
                          <span>Artist</span>
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2 border rounded-lg p-4 cursor-pointer hover:border-stagecloud-purple transition-colors flex-1">
                        <RadioGroupItem value="agency" id="agency" />
                        <Label htmlFor="agency" className="cursor-pointer flex items-center gap-2 w-full">
                          <Briefcase className="h-5 w-5" />
                          <span>Agency</span>
                        </Label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>} />

            <FormField control={registerForm.control} name="name" render={({
          field
        }) => <FormItem>
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input placeholder="John Doe" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>} />

            {userType === 'artist' && <FormField control={registerForm.control} name="artistName" render={({
          field
        }) => <FormItem>
                    <FormLabel>Artist/DJ Name</FormLabel>
                    <FormControl>
                      <Input placeholder="DJ Awesome" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>} />}

            {userType === 'venue' && <FormField control={registerForm.control} name="venueName" render={({
          field
        }) => <FormItem>
                    <FormLabel>Venue Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Club XYZ" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>} />}

            {userType === 'agency' && <FormField control={registerForm.control} name="agencyName" render={({
          field
        }) => <FormItem>
                    <FormLabel>Agency Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Talent Agency" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>} />}

            <FormField control={registerForm.control} name="email" render={({
          field
        }) => <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input type="email" placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>} />



            <FormField control={registerForm.control} name="password" render={({
          field
        }) => <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input type="password" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>} />

            <FormField control={registerForm.control} name="confirmPassword" render={({
          field
        }) => <FormItem>
                  <FormLabel>Confirm Password</FormLabel>
                  <FormControl>
                    <Input type="password" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>} />

            <Button type="submit" disabled={loading} className="w-full bg-stagecloud-purple hover:bg-stagecloud-purple/90 text-white bg-zinc-950 hover:bg-zinc-800">
              {loading ? <div className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating account...
                </div> : 'Create account'}
            </Button>
          </form>
        </Form>}
    </div>;
};
export default AuthForm;
