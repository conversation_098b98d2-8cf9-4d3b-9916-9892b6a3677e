
import { Navigate } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";
import { Loader2 } from "lucide-react";

interface ProtectedRouteProps {
  children: React.ReactNode;
  userType?: 'artist' | 'venue' | 'agency';
}

export const ProtectedRoute = ({ children, userType }: ProtectedRouteProps) => {
  const { user, profile, loading, isArtist, isVenue, isAgency } = useAuth();

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        <span className="ml-2 text-gray-500">Loading...</span>
      </div>
    );
  }

  // If not authenticated, redirect to login
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // If userType is specified, check if the user has the correct type using the isArtist, isVenue, isAgency flags
  if (userType && profile) {
    (`Checking user type: Required ${userType}, isArtist=${isArtist}, isVenue=${isVenue}, isAgency=${isAgency}`);

    // Check if user type doesn't match the required type
    const hasRequiredType =
      (userType === 'artist' && isArtist) ||
      (userType === 'venue' && isVenue) ||
      (userType === 'agency' && isAgency);

    if (!hasRequiredType) {
      (`User type mismatch: Required ${userType}, but user doesn't have this type`);

      // Redirect users to their appropriate dashboard based on their type
      if (isArtist && userType !== 'artist') {
        ('Redirecting to artist dashboard');
        return <Navigate to="/artist/dashboard" replace />;
      }

      if (isVenue && userType !== 'venue') {
        ('Redirecting to venue dashboard');
        return <Navigate to="/venue/dashboard" replace />;
      }

      if (isAgency && userType !== 'agency') {
        ('Redirecting to agency dashboard');
        return <Navigate to="/agency/dashboard" replace />;
      }
    }
  }

  // If profile is not loaded but userType is specified, use localStorage as fallback
  if (userType && !profile) {
    ('Profile not loaded, using localStorage fallback for user type check');
    const lastKnownUserType = localStorage.getItem('user_type');

    ('Last known user type from localStorage:', lastKnownUserType);

    // If we have a last known user type and it doesn't match the required type
    if (lastKnownUserType && lastKnownUserType !== userType) {
      (`User type mismatch from localStorage: Required ${userType}, but stored type is ${lastKnownUserType}`);

      // Redirect based on stored user type
      if (lastKnownUserType === 'artist' && userType !== 'artist') {
        ('Redirecting to artist dashboard based on localStorage');
        return <Navigate to="/artist/dashboard" replace />;
      }

      if (lastKnownUserType === 'venue' && userType !== 'venue') {
        ('Redirecting to venue dashboard based on localStorage');
        return <Navigate to="/venue/dashboard" replace />;
      }

      if (lastKnownUserType === 'agency' && userType !== 'agency') {
        ('Redirecting to agency dashboard based on localStorage');
        return <Navigate to="/agency/dashboard" replace />;
      }
    }
  }

  // If authenticated and has correct user type (or no specific type required), show the protected content
  return <>{children}</>;
};
