-- Agency Client Tags Rollback
-- Run this SQL in your Supabase SQL Editor to remove the tagging system
-- WARNING: This will permanently delete all tags and tag assignments

-- Drop the updated view first
DROP VIEW IF EXISTS public.agency_clients_with_contacts;

-- Drop tables (this will cascade and remove all data)
DROP TABLE IF EXISTS public.client_tag_assignments;
DROP TABLE IF EXISTS public.client_tags;

-- Recreate the original view without tags
CREATE OR REPLACE VIEW public.agency_clients_with_contacts AS
SELECT 
    c.*,
    COALESCE(
        json_agg(
            json_build_object(
                'id', cc.id,
                'name', cc.name,
                'email', cc.email,
                'phone', cc.phone,
                'position', cc.position,
                'is_primary', cc.is_primary,
                'created_at', cc.created_at,
                'updated_at', cc.updated_at
            ) ORDER BY cc.is_primary DESC, cc.name
        ) FILTER (WHERE cc.id IS NOT NULL),
        '[]'::json
    ) AS contacts
FROM public.agency_clients c
LEFT JOIN public.agency_client_contacts cc ON c.id = cc.client_id
GROUP BY c.id, c.agency_entity_id, c.name, c.type, c.email, c.phone, c.address, c.vat_number, c.notes, c.created_at, c.updated_at;

SELECT 'Agency Client Tags rollback completed!' as status;
