-- Agency Client Tags Migration
-- Run this SQL in your Supabase SQL Editor
-- This script adds the tagging system to existing client management

-- Create client_tags table for reusable tags
CREATE TABLE IF NOT EXISTS public.client_tags (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    agency_entity_id UUID NOT NULL REFERENCES public.entities(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    color TEXT DEFAULT '#6B7280', -- Default gray color
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(agency_entity_id, name) -- Prevent duplicate tag names per agency
);

-- Create client_tag_assignments table for many-to-many relationship
CREATE TABLE IF NOT EXISTS public.client_tag_assignments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    client_id UUID NOT NULL REFERENCES public.agency_clients(id) ON DELETE CASCADE,
    tag_id UUID NOT NULL REFERENCES public.client_tags(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(client_id, tag_id) -- Prevent duplicate assignments
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_client_tags_agency_entity_id ON public.client_tags(agency_entity_id);
CREATE INDEX IF NOT EXISTS idx_client_tags_name ON public.client_tags(name);
CREATE INDEX IF NOT EXISTS idx_client_tag_assignments_client_id ON public.client_tag_assignments(client_id);
CREATE INDEX IF NOT EXISTS idx_client_tag_assignments_tag_id ON public.client_tag_assignments(tag_id);

-- Create triggers for updated_at (only if trigger function exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'update_updated_at_column') THEN
        -- Create trigger only if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_client_tags_updated_at') THEN
            CREATE TRIGGER update_client_tags_updated_at 
                BEFORE UPDATE ON public.client_tags 
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
        END IF;
    END IF;
END $$;

-- Enable RLS for new tables
ALTER TABLE public.client_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_tag_assignments ENABLE ROW LEVEL SECURITY;

-- RLS policies for client_tags
DO $$
BEGIN
    -- Drop existing policies if they exist
    DROP POLICY IF EXISTS "Users can view tags for their agency" ON public.client_tags;
    DROP POLICY IF EXISTS "Users can insert tags for their agency" ON public.client_tags;
    DROP POLICY IF EXISTS "Users can update tags for their agency" ON public.client_tags;
    DROP POLICY IF EXISTS "Users can delete tags for their agency" ON public.client_tags;
    
    -- Create new policies
    CREATE POLICY "Users can view tags for their agency" ON public.client_tags
        FOR SELECT USING (
            agency_entity_id IN (
                SELECT entity_id FROM public.entity_users WHERE user_id = auth.uid()
            )
        );

    CREATE POLICY "Users can insert tags for their agency" ON public.client_tags
        FOR INSERT WITH CHECK (
            agency_entity_id IN (
                SELECT entity_id FROM public.entity_users WHERE user_id = auth.uid()
            )
        );

    CREATE POLICY "Users can update tags for their agency" ON public.client_tags
        FOR UPDATE USING (
            agency_entity_id IN (
                SELECT entity_id FROM public.entity_users WHERE user_id = auth.uid()
            )
        );

    CREATE POLICY "Users can delete tags for their agency" ON public.client_tags
        FOR DELETE USING (
            agency_entity_id IN (
                SELECT entity_id FROM public.entity_users WHERE user_id = auth.uid()
            )
        );
END $$;

-- RLS policies for client_tag_assignments
DO $$
BEGIN
    -- Drop existing policies if they exist
    DROP POLICY IF EXISTS "Users can view tag assignments for their agency clients" ON public.client_tag_assignments;
    DROP POLICY IF EXISTS "Users can insert tag assignments for their agency clients" ON public.client_tag_assignments;
    DROP POLICY IF EXISTS "Users can delete tag assignments for their agency clients" ON public.client_tag_assignments;
    
    -- Create new policies
    CREATE POLICY "Users can view tag assignments for their agency clients" ON public.client_tag_assignments
        FOR SELECT USING (
            client_id IN (
                SELECT ac.id FROM public.agency_clients ac
                WHERE ac.agency_entity_id IN (
                    SELECT entity_id FROM public.entity_users WHERE user_id = auth.uid()
                )
            )
        );

    CREATE POLICY "Users can insert tag assignments for their agency clients" ON public.client_tag_assignments
        FOR INSERT WITH CHECK (
            client_id IN (
                SELECT ac.id FROM public.agency_clients ac
                WHERE ac.agency_entity_id IN (
                    SELECT entity_id FROM public.entity_users WHERE user_id = auth.uid()
                )
            )
        );

    CREATE POLICY "Users can delete tag assignments for their agency clients" ON public.client_tag_assignments
        FOR DELETE USING (
            client_id IN (
                SELECT ac.id FROM public.agency_clients ac
                WHERE ac.agency_entity_id IN (
                    SELECT entity_id FROM public.entity_users WHERE user_id = auth.uid()
                )
            )
        );
END $$;

-- Update the agency_clients_with_contacts view to include tags
CREATE OR REPLACE VIEW public.agency_clients_with_contacts AS
SELECT 
    c.*,
    COALESCE(
        json_agg(
            json_build_object(
                'id', cc.id,
                'name', cc.name,
                'email', cc.email,
                'phone', cc.phone,
                'position', cc.position,
                'is_primary', cc.is_primary,
                'created_at', cc.created_at,
                'updated_at', cc.updated_at
            ) ORDER BY cc.is_primary DESC, cc.name
        ) FILTER (WHERE cc.id IS NOT NULL),
        '[]'::json
    ) AS contacts,
    COALESCE(
        json_agg(
            json_build_object(
                'id', ct.id,
                'name', ct.name,
                'color', ct.color
            ) ORDER BY ct.name
        ) FILTER (WHERE ct.id IS NOT NULL),
        '[]'::json
    ) AS tags
FROM public.agency_clients c
LEFT JOIN public.agency_client_contacts cc ON c.id = cc.client_id
LEFT JOIN public.client_tag_assignments cta ON c.id = cta.client_id
LEFT JOIN public.client_tags ct ON cta.tag_id = ct.id
GROUP BY c.id, c.agency_entity_id, c.name, c.type, c.email, c.phone, c.address, c.vat_number, c.notes, c.created_at, c.updated_at;

-- Grant permissions
GRANT ALL ON public.client_tags TO authenticated;
GRANT ALL ON public.client_tag_assignments TO authenticated;

-- Insert some sample tags (optional - remove if not needed)
-- INSERT INTO public.client_tags (agency_entity_id, name, color) VALUES
-- ('your-agency-entity-id', 'Club', '#3B82F6'),
-- ('your-agency-entity-id', 'Bar', '#10B981'),
-- ('your-agency-entity-id', 'Restaurant', '#F59E0B'),
-- ('your-agency-entity-id', 'Corporate', '#8B5CF6'),
-- ('your-agency-entity-id', 'VIP', '#EF4444');

-- Migration completed successfully
SELECT 'Agency Client Tags migration completed successfully!' as status;
