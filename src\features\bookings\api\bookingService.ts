import { supabase } from '@/core/api/supabase';
import { BookingData, ContractData } from '../types';

/**
 * Create a new booking
 * @param bookingData The booking data
 * @returns The created booking
 */
export const createBooking = async (bookingData: BookingData) => {
  try {
    const { data: booking, error: bookingError } = await supabase
      .from('bookings')
      .insert(bookingData)
      .select()
      .single();

    if (bookingError) throw bookingError;
    return booking;
  } catch (error) {
    console.error('Error creating booking:', error);
    throw error;
  }
};

/**
 * Create a contract for a booking
 * @param contractData The contract data
 * @returns The created contract
 */
export const createContract = async (contractData: ContractData) => {
  try {
    const { data: contract, error: contractError } = await supabase
      .from('documents')
      .insert(contractData)
      .select();

    if (contractError) throw contractError;
    return contract;
  } catch (error) {
    console.error('Error creating contract:', error);
    throw error;
  }
};

/**
 * Fetch contract templates for a user
 * @param userId The user ID
 * @param venueId The venue ID
 * @returns Array of contract templates
 */
export const fetchContractTemplates = async (userId: string, venueId: string) => {
  try {
    // Fetch templates owned by the current user's entity or associated with their venue
    // Use document_templates table and filter by document_type='contract'
    const { data, error } = await supabase
      .from('document_templates')
      .select('id, title, content')
      .eq('document_type', 'contract')
      .or(`venue_id.eq.${venueId},owner_entity_id.eq.${venueId}`);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching contract templates:', error);
    throw error;
  }
};

/**
 * Get venue details for a user
 * @param userId The user ID
 * @param userType The user type ('venue' or 'agency')
 * @returns The venue details
 */
export const getVenueDetails = async (userId: string, userType: 'venue' | 'agency' | 'artist') => {
  try {
    // For agency users, we need to get the agency entity
    if (userType === 'agency') {
      // Get the user's agency entity using the user_with_entities view
      const { data: entityUsers, error: entityUsersError } = await supabase
        .from('user_with_entities')
        .select('entity_id')
        .eq('user_id', userId)
        .eq('entity_type', 'agency');

      if (entityUsersError) throw entityUsersError;

      if (!entityUsers || entityUsers.length === 0) {
        throw new Error('No agency entity found for user');
      }

      const entityId = entityUsers[0].entity_id;

      // Get entity details
      const { data: entityData, error: entityError } = await supabase
        .from('entities')
        .select('name, description')
        .eq('id', entityId)
        .single();

      if (entityError) throw entityError;

      // For agency users, we'll use the agency entity as the venue
      return {
        id: entityId,
        venue_name: entityData.name,
        description: entityData.description,
        venue_type: 'agency',
        address: null,
        logo_url: null,
        banner_url: null,
        invoice_address: null,
        company_name: null,
        vat_number: null,
        updated_at: new Date().toISOString()
      };
    }

    // For venue users, get their venue entity
    const { data: entityUsers, error: entityUsersError } = await supabase
      .from('user_with_entities')
      .select('entity_id, is_primary')
      .eq('user_id', userId)
      .eq('entity_type', 'venue');

    if (entityUsersError) throw entityUsersError;

    if (!entityUsers || entityUsers.length === 0) {
      throw new Error('No venues found for this user');
    }

    // Get the primary venue or first venue
    const primaryVenue = entityUsers.find(v => v.is_primary) || entityUsers[0];
    const venueId = primaryVenue.entity_id;

    // Fetch venue details from the new entity-based structure
    const { data: venueProfileData, error: venueProfileError } = await supabase
      .from('venue_profiles')
      .select('*')
      .eq('entity_id', venueId)
      .single();

    if (venueProfileError) throw venueProfileError;

    const { data: entityData, error: entityError } = await supabase
      .from('entities')
      .select('name, description')
      .eq('id', venueId)
      .single();

    if (entityError) throw entityError;

    return {
      id: venueId,
      venue_name: entityData.name,
      description: entityData.description,
      venue_type: venueProfileData.venue_type,
      address: venueProfileData.address,
      logo_url: venueProfileData.logo_url,
      banner_url: venueProfileData.banner_url,
      invoice_address: venueProfileData.invoice_address,
      company_name: venueProfileData.company_name,
      vat_number: venueProfileData.vat_number,
      updated_at: venueProfileData.updated_at
    };
  } catch (error) {
    console.error('Error getting venue details:', error);
    throw error;
  }
};
