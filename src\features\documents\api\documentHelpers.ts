import { supabase } from '@/core/api/supabase';
import { getArtistDetailsForBooking } from '@/features/entities/api/artistHelpers';
import { getVenueDetailsForBooking } from '@/features/entities/api/entityProfileHelpers';

/**
 * Get document artist details
 */
export const getDocumentArtistDetails = async (artistId: string) => {
  try {
    // Get artist details from the entity-based schema
    return await getArtistDetailsForBooking(artistId);
  } catch (error) {
    console.error('Exception in getDocumentArtistDetails:', error);
    return null;
  }
};

/**
 * Get document venue details
 */
export const getDocumentVenueDetails = async (venueId: string) => {
  try {
    // Get venue details from the entity-based schema
    return await getVenueDetailsForBooking(venueId);
  } catch (error) {
    console.error('Exception in getDocumentVenueDetails:', error);
    return null;
  }
};

/**
 * Get all documents
 */
export const getAllDocuments = async () => {
  try {
    const { data, error } = await supabase
      .from('documents')
      .select(`
        id,
        title,
        document_type,
        booking_id,
        file_url,
        status,
        created_at,
        updated_at,
        description,
        signed_by_artist,
        signed_by_venue,
        created_by,
        bookings (
          booking_start,
          venue_id,
          artist_id,
          title
        )
      `);

    if (error) {
      console.error('Error fetching all documents:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception in getAllDocuments:', error);
    return null;
  }
};

/**
 * Upload document
 */
export const uploadDocument = async (
  file: File,
  bookingId: string,
  userId: string,
  title: string,
  description: string = '',
  documentType: string = 'pdf',
  isSignable: boolean = false
) => {
  try {
    // 1. Upload the file to storage
    const fileExt = file.name.split('.').pop();
    const fileName = `${Date.now()}_${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
    const filePath = `${bookingId}/${fileName}`;

    const { error: uploadError } = await supabase.storage
      .from('documents')
      .upload(filePath, file);

    if (uploadError) {
      console.error('Error uploading file:', uploadError);
      return { error: uploadError };
    }

    // 2. Get the public URL for the file
    const { data: { publicUrl } } = supabase.storage
      .from('documents')
      .getPublicUrl(filePath);

    // 3. Get the booking to determine the owner entity
    const { data: bookingData, error: bookingError } = await supabase
      .from('bookings')
      .select('owner_entity_id, venue_id')
      .eq('id', bookingId)
      .single();

    if (bookingError) {
      console.error('Error fetching booking for document:', bookingError);
      return { error: bookingError };
    }

    // Use owner_entity_id if available, otherwise fall back to venue_id
    const ownerEntityId = bookingData.owner_entity_id || bookingData.venue_id;

    // 4. Create a document record
    // Set the status based on whether the document is signable
    const status = isSignable ? 'pending' : 'uploaded';

    const { data: documentData, error: documentError } = await supabase
      .from('documents')
      .insert({
        title,
        description,
        document_type: documentType,
        file_url: publicUrl,
        file_type: fileExt,
        booking_id: bookingId,
        created_by: userId,
        owner_entity_id: ownerEntityId, // Set the owner entity ID
        status: status,
        is_signable: isSignable,
        signed_by_artist: false,
        signed_by_venue: false
      })
      .select();

    if (documentError) {
      console.error('Error creating document record:', documentError);
      return { error: documentError };
    }

    return { data: documentData[0] };
  } catch (error) {
    console.error('Exception in uploadDocument:', error);
    return { error };
  }
};

/**
 * Generate document from template
 */
export const generateDocumentFromTemplate = async (
  bookingId: string,
  templateId: string, // templateId is still needed to get the template content and type
  title: string,
  description: string,
  userId: string,
  formattedContent?: string,
  isSignable: boolean = false
) => {
  try {
    // 1. Get the booking to determine the owner entity
    const { data: bookingData, error: bookingError } = await supabase
      .from('bookings')
      .select('owner_entity_id, venue_id')
      .eq('id', bookingId)
      .single();

    if (bookingError) {
      console.error('Error fetching booking for document template:', bookingError);
      return { error: bookingError };
    }

    // Use owner_entity_id if available, otherwise fall back to venue_id
    const ownerEntityId = bookingData.owner_entity_id || bookingData.venue_id;

    // 2. Get the template to determine its type
    const { data: templateData, error: templateError } = await supabase
      .from('document_templates')
      .select('document_type')
      .eq('id', templateId)
      .single();

    if (templateError) {
      console.error('Error fetching template:', templateError);
      return { error: templateError };
    }

    // 3. Create the document
    const status = isSignable ? 'pending' : 'generated';

    const { data: documentData, error: documentError } = await supabase
      .from('documents')
      .insert({
        title,
        description,
        document_type: templateData.document_type,
        booking_id: bookingId,
        template_id: templateId,
        content: formattedContent,
        created_by: userId,
        owner_entity_id: ownerEntityId, // Set the owner entity ID
        status: status,
        is_signable: isSignable,
        signed_by_artist: false,
        signed_by_venue: false
      })
      .select();

    if (documentError) {
      console.error('Error creating document from template:', documentError);
      return { error: documentError };
    }

    return { data: documentData[0] };
  } catch (error) {
    console.error('Exception in generateDocumentFromTemplate:', error);
    return { error };
  }
};

/**
 * Legacy function for backward compatibility
 */
export const generateContractDocument = generateDocumentFromTemplate;

/**
 * Toggle document signature
 */
export const toggleDocumentSignature = async (
  documentId: string,
  userId: string,
  userType: 'artist' | 'venue'
) => {
  try {
    // First, get the current document to check signature status
    const { data: currentDoc, error: fetchError } = await supabase
      .from('documents')
      .select('signed_by_artist, signed_by_venue, document_type')
      .eq('id', documentId)
      .single();

    if (fetchError) {
      console.error('Error fetching document:', fetchError);
      return { error: fetchError };
    }

    // Call the RPC function to toggle the signature
    const { data, error } = await supabase.rpc(
      'toggle_document_signature',
      {
        p_document_id: documentId,
        p_user_id: userId,
        p_user_type: userType
      }
    );

    if (error) {
      console.error('Error toggling signature:', error);
      return { error };
    }

    // Now update the document status based on the new signature state
    // We need to determine what the new signature state will be (it's toggled)
    const willBeSignedByArtist = userType === 'artist' ? !currentDoc.signed_by_artist : currentDoc.signed_by_artist;
    const willBeSignedByVenue = userType === 'venue' ? !currentDoc.signed_by_venue : currentDoc.signed_by_venue;

    // Determine the new status
    let newStatus = 'pending';
    if (willBeSignedByArtist && willBeSignedByVenue) {
      newStatus = 'signed';
    } else if (!willBeSignedByArtist && !willBeSignedByVenue) {
      newStatus = 'draft';
    }

    // Update the document status
    const { error: updateError } = await supabase
      .from('documents')
      .update({ status: newStatus })
      .eq('id', documentId);

    if (updateError) {
      console.error('Error updating document status:', updateError);
      // We don't return an error here because the signature was successfully toggled
      // The status update is secondary
    }

    return { success: data };
  } catch (error) {
    console.error('Exception in toggleDocumentSignature:', error);
    return { error };
  }
};

/**
 * Get document templates
 */
export const getDocumentTemplates = async (entityId: string, documentType?: string) => {
  try {
    // First try to get templates using owner_entity_id
    let query = supabase
      .from('document_templates')
      .select('*')
      .eq('owner_entity_id', entityId);

    if (documentType) {
      query = query.eq('document_type', documentType);
    }

    const { data: ownerEntityData, error: ownerEntityError } = await query;

    if (ownerEntityError) {
      console.error('Error fetching document templates by owner_entity_id:', ownerEntityError);
    } else if (ownerEntityData && ownerEntityData.length > 0) {
      return ownerEntityData;
    }

    // Fall back to venue_id for backward compatibility
    let fallbackQuery = supabase
      .from('document_templates')
      .select('*')
      .eq('venue_id', entityId);

    if (documentType) {
      fallbackQuery = fallbackQuery.eq('document_type', documentType);
    }

    const { data, error } = await fallbackQuery;

    if (error) {
      console.error('Error fetching document templates by venue_id:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Exception in getDocumentTemplates:', error);
    return null;
  }
};

/**
 * Legacy function for backward compatibility
 */
export const getContractTemplates = async (venueId: string) => {
  return getDocumentTemplates(venueId, 'contract');
};

/**
 * Debug document access
 */
export const debugDocumentAccess = async (documentId: string, userId: string) => {
  try {
    const { data, error } = await supabase.rpc('debug_document_access', {
      p_document_id: documentId,
      p_user_id: userId
    });

    if (error) {
      console.error('Error debugging document access:', error);
      return { error };
    }

    return { data };
  } catch (error) {
    console.error('Exception in debugDocumentAccess:', error);
    return { error };
  }
};

/**
 * Get document by ID
 */
export const getDocumentById = async (documentId: string) => {
  try {
    // Get the document with all its fields
    const result = await supabase
      .from('documents')
      .select(`
        id,
        title,
        document_type,
        booking_id,
        file_url,
        file_type,
        content,
        status,
        created_at,
        updated_at,
        description,
        signed_by_artist,
        signed_by_venue,
        created_by,
        is_signable,
        is_public,
        share_id,
        artist_signature_date,
        venue_signature_date,
        owner_entity_id
      `)
      .eq('id', documentId)
      .single();

    if (result.error) {
      console.error('Error fetching document:', result.error);
      return null;
    }

    const document = result.data;
    if (!document) {
      console.error('Document not found');
      return null;
    }

    // If there's a booking associated, get the booking details
    if (document.booking_id) {
      const bookingResult = await supabase
        .from('bookings')
        .select(`
          id,
          title,
          booking_start,
          booking_end,
          venue_id,
          artist_id,
          location,
          price,
          pricing_type,
          owner_entity_id
        `)
        .eq('id', document.booking_id)
        .single();

      if (!bookingResult.error && bookingResult.data) {
        const booking = bookingResult.data;

        // Add booking information to the document
        return {
          ...document,
          booking_title: booking.title,
          booking_start: booking.booking_start,
          booking_end: booking.booking_end,
          venue_id: booking.venue_id,
          artist_id: booking.artist_id
        };
      } else if (bookingResult.error) {
        console.error('Error fetching booking:', bookingResult.error);
      }
    }

    return document;
  } catch (error) {
    console.error('Exception in getDocumentById:', error);
    return null;
  }
};

/**
 * Delete document
 */
export const deleteDocument = async (documentId: string) => {
  try {
    const { error } = await supabase
      .from('documents')
      .delete()
      .eq('id', documentId);

    if (error) {
      console.error('Error deleting document:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Exception in deleteDocument:', error);
    throw error;
  }
};

/**
 * Generate document share ID
 */
export const generateDocumentShareId = async (documentId: string) => {
  try {
    // Generate a UUID for the share ID
    const shareId = crypto.randomUUID();

    // Update the document with the new share ID and set it to public
    const { data, error } = await supabase
      .from('documents')
      .update({
        share_id: shareId,
        is_public: true
      })
      .eq('id', documentId)
      .select('share_id')
      .single();

    if (error) {
      console.error('Error generating share ID:', error);
      return { error };
    }

    return { data: data.share_id };
  } catch (error) {
    console.error('Exception in generateDocumentShareId:', error);
    return { error };
  }
};

/**
 * Toggle document public status
 */
export const toggleDocumentPublicStatus = async (documentId: string) => {
  try {
    // First, get the current document to check its public status and share_id
    const { data: currentDoc, error: fetchError } = await supabase
      .from('documents')
      .select('is_public, share_id')
      .eq('id', documentId)
      .single();

    if (fetchError) {
      console.error('Error fetching document:', fetchError);
      return { error: fetchError };
    }

    // If the document is not public and doesn't have a share_id, generate one
    if (!currentDoc.is_public && !currentDoc.share_id) {
      const result = await generateDocumentShareId(documentId);
      if (result.error) {
        return { error: result.error };
      }
      return { data: true }; // Document is now public
    }

    // Otherwise, just toggle the is_public flag
    const newPublicStatus = !currentDoc.is_public;
    const { error: updateError } = await supabase
      .from('documents')
      .update({ is_public: newPublicStatus })
      .eq('id', documentId);

    if (updateError) {
      console.error('Error updating document public status:', updateError);
      return { error: updateError };
    }

    return { data: newPublicStatus };
  } catch (error) {
    console.error('Exception in toggleDocumentPublicStatus:', error);
    return { error };
  }
};

/**
 * Get user documents
 */
export const getUserDocuments = async (userId: string, userType: string) => {
  try {
    // First get all entities this user is associated with
    const { data: userEntities, error: entitiesError } = await supabase
      .from('entity_users')
      .select('entity_id')
      .eq('user_id', userId);

    if (entitiesError) {
      console.error('Error fetching user entities:', entitiesError);
      return null;
    }

    // If no entities found, try using the user ID directly (for backward compatibility)
    if (!userEntities || userEntities.length === 0) {
      // First try to find bookings where the user is the owner
      const { data: ownerBookings, error: ownerError } = await supabase
        .from('bookings')
        .select('id')
        .eq('owner_entity_id', userId);

      if (ownerError) {
        console.error('Error fetching bookings by owner_entity_id:', ownerError);
      }

      // If we found bookings by owner_entity_id, use those
      if (ownerBookings && ownerBookings.length > 0) {
        const bookings = ownerBookings;

        // Get documents for these bookings
        const { data: documents, error: documentsError } = await supabase
          .from('documents')
          .select(`
            id,
            title,
            document_type,
            booking_id,
            file_url,
            status,
            created_at,
            description,
            signed_by_artist,
            signed_by_venue,
            bookings (
              booking_start,
              venue_id,
              artist_id,
              title
            )
          `)
          .in('booking_id', bookings.map((b: any) => b.id));

        if (documentsError) {
          console.error('Error fetching documents:', documentsError);
          return null;
        }

        return documents;
      }

      // Otherwise, fall back to the old way based on user type
      let bookingsQuery: any;

      if (userType === 'artist') {
        bookingsQuery = supabase
          .from('bookings')
          .select('id')
          .eq('artist_id', userId);
      } else if (userType === 'venue' || userType === 'agency') {
        bookingsQuery = supabase
          .from('bookings')
          .select('id')
          .eq('venue_id', userId);
      } else {
        // Invalid user type
        return null;
      }

      const { data: bookings, error: bookingsError } = await bookingsQuery;

      if (bookingsError) {
        console.error('Error fetching bookings:', bookingsError);
        return null;
      }

      if (!bookings || bookings.length === 0) return [];

      // Now get all documents for these bookings
      const { data: documents, error: documentsError } = await supabase
        .from('documents')
        .select(`
          id,
          title,
          document_type,
          booking_id,
          file_url,
          status,
          created_at,
          description,
          signed_by_artist,
          signed_by_venue,
          bookings (
            booking_start,
            venue_id,
            artist_id,
            title
          )
        `)
        .in('booking_id', bookings.map((b: any) => b.id));

      if (documentsError) {
        console.error('Error fetching documents:', documentsError);
        return null;
      }

      return documents;
    }

    // Get all entity IDs
    const entityIds = userEntities.map(e => e.entity_id);

    // Get all bookings where this user's entities are the owner or they are the artist/venue
    const { data: bookings, error: bookingsError } = await supabase
      .from('bookings')
      .select('id')
      .or(`owner_entity_id.in.(${entityIds.join(',')}),venue_id.in.(${entityIds.join(',')}),artist_id.in.(${entityIds.join(',')})`)
      .order('booking_start', { ascending: false });

    if (bookingsError) {
      console.error('Error fetching bookings for entities:', bookingsError);
      return null;
    }

    if (!bookings || bookings.length === 0) {
      // Also check for bookings created by this user
      const { data: createdBookings, error: createdError } = await supabase
        .from('bookings')
        .select('id')
        .eq('created_by', userId);

      if (createdError) {
        console.error('Error fetching created bookings:', createdError);
        return null;
      }

      if (!createdBookings || createdBookings.length === 0) return [];

      // Get documents for bookings created by this user
      const { data: documents, error: documentsError } = await supabase
        .from('documents')
        .select(`
          id,
          title,
          document_type,
          booking_id,
          file_url,
          status,
          created_at,
          description,
          signed_by_artist,
          signed_by_venue,
          bookings (
            booking_start,
            venue_id,
            artist_id,
            title
          )
        `)
        .in('booking_id', createdBookings.map((b: any) => b.id));

      if (documentsError) {
        console.error('Error fetching documents for created bookings:', documentsError);
        return null;
      }

      return documents;
    }

    // Get documents for all bookings
    const { data: documents, error: documentsError } = await supabase
      .from('documents')
      .select(`
        id,
        title,
        document_type,
        booking_id,
        file_url,
        status,
        created_at,
        description,
        signed_by_artist,
        signed_by_venue,
        bookings (
          booking_start,
          venue_id,
          artist_id,
          title
        )
      `)
      .in('booking_id', bookings.map((b: any) => b.id));

    if (documentsError) {
      console.error('Error fetching documents for entity bookings:', documentsError);
      return null;
    }

    // Also get documents created by this user
    const { data: createdDocuments, error: createdDocError } = await supabase
      .from('documents')
      .select(`
        id,
        title,
        document_type,
        booking_id,
        file_url,
        status,
        created_at,
        description,
        signed_by_artist,
        signed_by_venue,
        bookings (
          booking_start,
          venue_id,
          artist_id,
          title
        )
      `)
      .eq('created_by', userId);

    if (createdDocError) {
      console.error('Error fetching created documents:', createdDocError);
      return null;
    }

    // Combine and deduplicate documents
    const allDocuments = [...(documents || []), ...(createdDocuments || [])];
    const uniqueDocuments = Array.from(new Map(allDocuments.map(doc => [doc.id, doc])).values());

    return uniqueDocuments;
  } catch (error) {
    console.error('Exception in getUserDocuments:', error);
    return null;
  }
};

/**
 * Get document by share ID
 */
export const getDocumentByShareId = async (shareId: string) => {
  try {
    // Get the document with all its fields and related booking information
    const result = await supabase
      .from('documents')
      .select(`
        id,
        title,
        document_type,
        booking_id,
        file_url,
        file_type,
        content,
        status,
        created_at,
        updated_at,
        description,
        signed_by_artist,
        signed_by_venue,
        created_by,
        is_signable,
        is_public,
        share_id,
        artist_signature_date,
        venue_signature_date,
        bookings (
          id,
          title,
          booking_start,
          booking_end,
          venue_id,
          artist_id,
          location,
          price,
          pricing_type,
          owner_entity_id
        )
      `)
      .eq('share_id', shareId)
      .eq('is_public', true)  // Only return the document if it's public
      .maybeSingle();

    if (result.error) {
      console.error('Error fetching document by share ID:', result.error);
      return null;
    }

    if (!result.data) {
      console.error('Document not found or not public');
      return null;
    }

    // Extract booking information if available
    const document = result.data;
    const booking = document.bookings;

    if (booking) {
      return {
        ...document,
        booking_title: booking.title,
        booking_start: booking.booking_start,
        booking_end: booking.booking_end,
        venue_id: booking.venue_id,
        artist_id: booking.artist_id
      };
    }

    return document;
  } catch (error) {
    console.error('Exception in getDocumentByShareId:', error);
    return null;
  }
};
